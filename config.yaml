# HSFLNet 训练配置文件
# 使用方法: python train.py --config config.yaml

# ============================================================================
# 核心参数 - 最重要的参数，通常需要调整
# ============================================================================
core:
  dataset: 'sysu'           # 数据集名称: sysu, regdb, llcm
  batch_size: 4             # 训练批次大小 (重要: 影响内存和收敛)
  lr: 0.1                   # 学习率 (重要: SGD=0.1, Adam=0.00035)
  gpu: 0                    # GPU设备ID
  seed: 0                   # 随机种子 (重要: 影响结果复现)

# ============================================================================
# 超图卷积参数 - HSFLNet的核心创新
# ============================================================================
hypergraph:
  # Beta分布核心参数
  use_beta_distribution: false  # 是否使用Beta分布生成概率连接矩阵                                       
  graphw: 1.0                  # 超图卷积权重参数 (重要: 控制超图影响强度)

  # LSE^HVD对角局部性参数
  use_diagonal: true           # 是否使用对角超图卷积

  # 其他超图参数
  theta1: 0.0                  # 超图稀疏化阈值参数
  edge: 256                    # 超边数量
  max_depth: 3                 # 辅助节点的最大深度
  diagonal_threshold: 0.9      # 对角相似度阈值
  simplified_diagonal: false   # 是否使用简化的对角超图卷积模式
  fusion_strategy: 'advanced'  # 辅助节点融合策略: simple_average, weighted_attention, advanced

# ============================================================================
# 跨模态对比学习参数 - 新增功能
# ============================================================================
cross_modal:
  # 核心开关
  enable: false                 # 是否启用跨模态对比学习

  # 条带划分参数
  num_strips: 4                # 水平条带数量 (建议: 3-5)

  # 对比学习参数
  temperature: 0.07            # 对比学习温度参数 (建议: 0.05-0.1)
  contrastive_weight: 0.5      # 对比损失权重 (建议: 0.1-1.0)

  # 特征融合参数
  projection_dim: 128          # 投影头输出维度 (建议: 64-256)
  num_heads: 4                 # 多头注意力头数 (建议: 2-8)

  # 优化参数
  use_vectorized: true         # 是否使用向量化计算优化GPU利用率
  gradient_checkpointing: false # 是否使用梯度检查点节省内存

# ============================================================================
# 网络架构参数
# ============================================================================
model:
  arch: 'resnet50'             # 网络架构: resnet18, resnet50
  backbone: 'AGW'              # 骨干网络

# ============================================================================
# 训练控制参数
# ============================================================================
training:
  # 基本控制
  resume: ''                   # 恢复训练的检查点路径
  test_only: false             # 是否只进行测试          

  # 优化器设置
  optim: 'SGD'                 # 优化器类型: SGD, Adam
  lr_scheduler: 'step'         # 学习率调度器: step, cosine

  # 损失函数
  loss_tri: 'DMC'              # 三元组损失类型
  margin: 0.7                  # 三元组损失边界

# ============================================================================
# 数据处理参数
# ============================================================================
data:
  # 图像尺寸
  img_w: 192                   # 图像宽度
  img_h: 384                   # 图像高度

  # 数据加载
  workers: 4                   # 数据加载线程数
  test_batch: 64               # 测试批次大小
  num_pos: 4                   # 每个身份的正样本数量

  # 数据集特定
  trial: 1                     # RegDB数据集的试验编号
  mode: 'all'                  # 模式: all, indoor


# 路径配置
paths:
  model_path: 'result/saved_model/'     # 模型保存路径
  log_path: 'result/log/'               # 日志保存路径
  vis_log_path: 'result/log/vis_log/'   # 可视化日志路径


# Beta分布数据集特定参数 - 核心创新参数
beta_distribution:
  # SYSU-MM01 数据集参数
  sysu:
    visible_original:
      alpha: 2.5  # 可见光原图Alpha基础参数
      beta: 1.2   # 可见光原图Beta基础参数
    visible_enhanced:
      alpha: 2.0  # 可见光增强图Alpha基础参数
      beta: 1.5   # 可见光增强图Beta基础参数
    thermal_original:
      alpha: 2.0  # 红外原图Alpha基础参数
      beta: 1.3   # 红外原图Beta基础参数
    thermal_enhanced:
      alpha: 2.8  # 红外增强图Alpha基础参数
      beta: 1.0   # 红外增强图Beta基础参数
  
  # LLCM 数据集参数
  llcm:
    visible_original:
      alpha: 3.0  # 可见光原图Alpha基础参数
      beta: 1.0   # 可见光原图Beta基础参数
    visible_enhanced:
      alpha: 2.2  # 可见光增强图Alpha基础参数
      beta: 1.4   # 可见光增强图Beta基础参数
    thermal_original:
      alpha: 2.5  # 红外原图Alpha基础参数
      beta: 1.1   # 红外原图Beta基础参数
    thermal_enhanced:
      alpha: 3.2  # 红外增强图Alpha基础参数
      beta: 0.8   # 红外增强图Beta基础参数
  
  # RegDB 数据集参数
  regdb:
    visible_original:
      alpha: 2.8  # 可见光原图Alpha基础参数
      beta: 1.1   # 可见光原图Beta基础参数
    visible_enhanced:
      alpha: 2.3  # 可见光增强图Alpha基础参数
      beta: 1.3   # 可见光增强图Beta基础参数
    thermal_original:
      alpha: 2.6  # 红外原图Alpha基础参数
      beta: 1.0   # 红外原图Beta基础参数
    thermal_enhanced:
      alpha: 3.0  # 红外增强图Alpha基础参数
      beta: 0.9   # 红外增强图Beta基础参数

# ============================================================================
# 实验配置模板 - 可以快速切换不同的实验设置
# ============================================================================
experiments:
  # 快速调试配置 - 小参数快速测试
  debug:
    core:
      batch_size: 2
    data:
      test_batch: 32
      workers: 2
    hypergraph:
      edge: 64
      max_depth: 2
    training:
      save_epoch: 1

# ============================================================================
# 参数调优建议和说明
# ============================================================================
tuning_guide:
  alpha_range: [1.5, 4.0]  # Alpha参数建议范围
  beta_range: [0.8, 2.0]   # Beta参数建议范围
  graphw_range: [0.5, 2.0] # 超图权重建议范围
  
  tips:
    - "Alpha参数控制连接强度，值越大连接越强"
    - "Beta参数控制连接稳定性，值越大越保守"
    - "对于特征丰富的模态（如可见光），可以使用较高的alpha值"
    - "对于噪声较多的数据，适当降低alpha值"
    - "热成像数据通常需要较低的beta值"
    - "初始训练时graphw可以使用较小的值（0.5-1.0）"
