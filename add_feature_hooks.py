#!/usr/bin/env python3
"""
在现有模型中添加特征钩子来捕获BN后和超图卷积后的特征
这是最简单的方法，只需要修改model.py中的几行代码
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
import os

# 全局变量存储特征
captured_features = {
    'bn_features': [],
    'graph_features': [],
    'labels': [],
    'part_indices': []
}

def clear_captured_features():
    """清空捕获的特征"""
    global captured_features
    captured_features = {
        'bn_features': [],
        'graph_features': [],
        'labels': [],
        'part_indices': []
    }

def save_features_for_visualization(bn_feat, graph_feat, part_idx, label=None):
    """
    保存特征用于可视化
    这个函数需要在model.py的forward函数中调用
    
    Args:
        bn_feat: BN后的特征 [B, C, H, W]
        graph_feat: 超图卷积后的特征 [B, C, H, W]
        part_idx: part索引
        label: 标签（可选）
    """
    global captured_features
    
    # 只在评估模式下保存特征，避免训练时内存溢出
    if bn_feat.requires_grad:
        return
    
    # 池化并展平特征
    bn_pooled = torch.nn.functional.avg_pool2d(bn_feat, bn_feat.size()[2:])
    bn_pooled = bn_pooled.view(bn_pooled.size(0), -1)
    
    graph_pooled = torch.nn.functional.avg_pool2d(graph_feat, graph_feat.size()[2:])
    graph_pooled = graph_pooled.view(graph_pooled.size(0), -1)
    
    # 转换为numpy并保存
    captured_features['bn_features'].append(bn_pooled.detach().cpu().numpy())
    captured_features['graph_features'].append(graph_pooled.detach().cpu().numpy())
    captured_features['part_indices'].extend([part_idx] * bn_pooled.size(0))
    
    if label is not None:
        if isinstance(label, torch.Tensor):
            label = label.cpu().numpy()
        if np.isscalar(label):
            captured_features['labels'].extend([label] * bn_pooled.size(0))
        else:
            captured_features['labels'].extend(label.tolist())
    else:
        captured_features['labels'].extend([0] * bn_pooled.size(0))

def analyze_captured_features():
    """分析捕获的特征"""
    global captured_features
    
    if not captured_features['bn_features']:
        print("没有捕获到特征数据")
        return None
    
    # 转换为numpy数组
    bn_features = np.vstack(captured_features['bn_features'])
    graph_features = np.vstack(captured_features['graph_features'])
    labels = np.array(captured_features['labels'])
    part_indices = np.array(captured_features['part_indices'])
    
    print(f"捕获的特征数量: {len(bn_features)}")
    print(f"特征维度: {bn_features.shape[1]}")
    print(f"标签数量: {len(np.unique(labels))}")
    print(f"Part数量: {len(np.unique(part_indices))}")
    
    # 计算统计信息
    stats = {}
    
    # 特征范数
    bn_norms = np.linalg.norm(bn_features, axis=1)
    graph_norms = np.linalg.norm(graph_features, axis=1)
    
    stats['bn_norm_mean'] = bn_norms.mean()
    stats['bn_norm_std'] = bn_norms.std()
    stats['graph_norm_mean'] = graph_norms.mean()
    stats['graph_norm_std'] = graph_norms.std()
    
    print(f"BN特征范数: 均值={stats['bn_norm_mean']:.4f}, 标准差={stats['bn_norm_std']:.4f}")
    print(f"超图特征范数: 均值={stats['graph_norm_mean']:.4f}, 标准差={stats['graph_norm_std']:.4f}")
    
    # 余弦相似度
    similarities = []
    for i in range(len(bn_features)):
        sim = np.dot(bn_features[i], graph_features[i]) / (
            np.linalg.norm(bn_features[i]) * np.linalg.norm(graph_features[i]) + 1e-8
        )
        similarities.append(sim)
    
    similarities = np.array(similarities)
    stats['similarity_mean'] = similarities.mean()
    stats['similarity_std'] = similarities.std()
    
    print(f"余弦相似度: 均值={stats['similarity_mean']:.4f}, 标准差={stats['similarity_std']:.4f}")
    
    return stats, bn_features, graph_features, labels, part_indices

def visualize_captured_features(save_dir='feature_vis'):
    """可视化捕获的特征"""
    stats, bn_features, graph_features, labels, part_indices = analyze_captured_features()
    
    if stats is None:
        return
    
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 特征分布对比
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 范数分布
    bn_norms = np.linalg.norm(bn_features, axis=1)
    graph_norms = np.linalg.norm(graph_features, axis=1)
    
    axes[0, 0].hist(bn_norms, bins=30, alpha=0.7, label='BN特征', color='blue')
    axes[0, 0].hist(graph_norms, bins=30, alpha=0.7, label='超图特征', color='red')
    axes[0, 0].set_title('特征范数分布')
    axes[0, 0].set_xlabel('L2范数')
    axes[0, 0].legend()
    
    # 相似度分布
    similarities = []
    for i in range(len(bn_features)):
        sim = np.dot(bn_features[i], graph_features[i]) / (
            np.linalg.norm(bn_features[i]) * np.linalg.norm(graph_features[i]) + 1e-8
        )
        similarities.append(sim)
    
    axes[0, 1].hist(similarities, bins=30, alpha=0.7, color='green')
    axes[0, 1].set_title('余弦相似度分布')
    axes[0, 1].set_xlabel('余弦相似度')
    
    # 特征均值对比
    bn_mean = bn_features.mean(axis=0)
    graph_mean = graph_features.mean(axis=0)
    dim_to_show = min(50, len(bn_mean))
    
    axes[1, 0].plot(bn_mean[:dim_to_show], label='BN特征', color='blue')
    axes[1, 0].plot(graph_mean[:dim_to_show], label='超图特征', color='red')
    axes[1, 0].set_title(f'特征均值对比 (前{dim_to_show}维)')
    axes[1, 0].legend()
    
    # 特征差异
    diff = np.abs(bn_features - graph_features).mean(axis=0)
    axes[1, 1].plot(diff[:dim_to_show], color='purple')
    axes[1, 1].set_title(f'特征差异 (前{dim_to_show}维)')
    axes[1, 1].set_xlabel('特征维度')
    axes[1, 1].set_ylabel('平均绝对差异')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_statistics.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. t-SNE可视化（如果数据量足够）
    if len(bn_features) > 50:
        print("生成t-SNE可视化...")
        
        # 随机采样
        n_vis = min(500, len(bn_features))
        indices = np.random.choice(len(bn_features), n_vis, replace=False)
        
        bn_vis = bn_features[indices]
        graph_vis = graph_features[indices]
        labels_vis = labels[indices]
        
        # t-SNE降维
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, n_vis//4))
        bn_2d = tsne.fit_transform(bn_vis)
        graph_2d = tsne.fit_transform(graph_vis)
        
        # 绘制
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        unique_labels = np.unique(labels_vis)[:10]  # 只显示前10个类别
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_labels)))
        
        for i, label in enumerate(unique_labels):
            mask = labels_vis == label
            if np.any(mask):
                axes[0].scatter(bn_2d[mask, 0], bn_2d[mask, 1], 
                               c=[colors[i]], label=f'ID {label}', alpha=0.7, s=20)
                axes[1].scatter(graph_2d[mask, 0], graph_2d[mask, 1], 
                               c=[colors[i]], label=f'ID {label}', alpha=0.7, s=20)
        
        axes[0].set_title('BN后特征 (t-SNE)')
        axes[1].set_title('超图卷积后特征 (t-SNE)')
        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        plt.savefig(f'{save_dir}/feature_tsne.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    print(f"可视化结果已保存到 {save_dir}/")
    return stats

def print_model_modification_instructions():
    """打印模型修改说明"""
    print("""
=== 模型修改说明 ===

要启用特征捕获，需要在 model.py 的 forward 函数中添加以下代码：

1. 在文件开头添加导入：
   from add_feature_hooks import save_features_for_visualization

2. 在 forward 函数中，找到这两行代码：
   x = self.bn_conv_reduce(x)  # add a BN
   feat = self.graphw * self.hypergraph(x)

3. 在这两行之间添加：
   # 保存特征用于可视化分析
   if not self.training and hasattr(self, '_capture_features') and self._capture_features:
       save_features_for_visualization(x, feat, i, identities)

4. 在测试前设置捕获标志：
   net._capture_features = True

5. 运行测试后调用可视化：
   from add_feature_hooks import visualize_captured_features
   visualize_captured_features()

完整的修改示例：
```python
# 在 model.py 的 forward 函数中
for i in range(self.part_num):
    mask = masks[:, i:i+1, :, :]
    x = mask*global_feat
    x = self.conv_reduce(x)
    x = self.bn_conv_reduce(x)  # add a BN
    
    # 超图卷积
    feat = self.graphw * self.hypergraph(x)
    
    # 保存特征用于可视化分析
    if not self.training and hasattr(self, '_capture_features') and self._capture_features:
        save_features_for_visualization(x, feat, i, identities)
    
    # 继续后续处理...
```
""")

if __name__ == "__main__":
    print_model_modification_instructions()
