import torch
import torch.nn as nn
from torch.nn import init
from resnet import resnet50
from torch.nn import functional as F
from hypergraphs import *
from cross_modal_contrastive import VectorizedCrossModalContrastive, create_horizontal_strips_batch

# 特征可视化相关
try:
    from add_feature_hooks import save_features_for_visualization
    FEATURE_VIS_AVAILABLE = True
except ImportError:
    FEATURE_VIS_AVAILABLE = False

class Normalize(nn.Module):
    def __init__(self, power=2):
        super(Normalize, self).__init__()
        self.power = power

    def forward(self, x):
        norm = x.pow(self.power).sum(1, keepdim=True).pow(1. / self.power)
        out = x.div(norm)
        return out


def weights_init_kaiming(m):
    classname = m.__class__.__name__
    # print(classname)
    if classname.find('Conv') != -1:
        init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
    elif classname.find('Linear') != -1:
        init.kaiming_normal_(m.weight.data, a=0, mode='fan_out')
        init.zeros_(m.bias.data)
    elif classname.find('BatchNorm1d') != -1:
        init.normal_(m.weight.data, 1.0, 0.01)
        init.zeros_(m.bias.data)


def weights_init_classifier(m):
    classname = m.__class__.__name__
    if classname.find('Linear') != -1:
        init.normal_(m.weight.data, 0, 0.001)
        if m.bias:
            init.zeros_(m.bias.data)


class visible_module(nn.Module):
    def __init__(self, arch='resnet50'):
        super(visible_module, self).__init__()
        _ = arch  # Keep for potential future use

        model_v = resnet50(pretrained=True,
                           last_conv_stride=1, last_conv_dilation=1)
        # avg pooling to global pooling
        self.visible = model_v
        #self.conv1 = nn.Conv2d(64, 64, kernel_size=7,padding=3,bias=False)

    def forward(self, x):
        x = self.visible.conv1(x)
        #x = self.conv1(x)
        x = self.visible.bn1(x)
        x = self.visible.relu(x)
        x = self.visible.maxpool(x)
        return x


class thermal_module(nn.Module):
    def __init__(self, arch='resnet50'):
        super(thermal_module, self).__init__()
        _ = arch  # Keep for potential future use

        model_t = resnet50(pretrained=True,
                           last_conv_stride=1, last_conv_dilation=1)
        # avg pooling to global pooling
        self.thermal = model_t
        #self.conv1 = nn.Conv2d(64, 64, kernel_size=7,padding=3,bias=False)

    def forward(self, x):
        x = self.thermal.conv1(x)
        #x = self.conv1(x)
        x = self.thermal.bn1(x)
        x = self.thermal.relu(x)
        x = self.thermal.maxpool(x)
        return x


class visible_moduleA(nn.Module):
    def __init__(self, arch='resnet50'):
        super(visible_moduleA, self).__init__()
        _ = arch  # Keep for potential future use

        model_v = resnet50(pretrained=True,
                           last_conv_stride=1, last_conv_dilation=1)
        # avg pooling to global pooling
        self.visible = model_v
        #self.conv1 = nn.Conv2d(64, 64, kernel_size=7,padding=3,bias=False)

    def forward(self, x):
        x = self.visible.conv1(x)
        #x = self.conv1(x)
        x = self.visible.bn1(x)
        x = self.visible.relu(x)
        x = self.visible.maxpool(x)
        return x


class thermal_moduleA(nn.Module):
    def __init__(self, arch='resnet50'):
        super(thermal_moduleA, self).__init__()
        _ = arch  # Keep for potential future use

        model_t = resnet50(pretrained=True,
                           last_conv_stride=1, last_conv_dilation=1)
        # avg pooling to global pooling
        self.thermal = model_t
        #self.conv1 = nn.Conv2d(64, 64, kernel_size=7,padding=3,bias=False)

    def forward(self, x):
        x = self.thermal.conv1(x)
        #x = self.conv1(x)
        x = self.thermal.bn1(x)
        x = self.thermal.relu(x)
        x = self.thermal.maxpool(x)
        return x


class base_resnet(nn.Module):
    def __init__(self, arch='resnet50'):
        super(base_resnet, self).__init__()
        _ = arch  # Keep for potential future use

        model_base = resnet50(pretrained=True,
                              last_conv_stride=1, last_conv_dilation=1)
        # avg pooling to global pooling
        model_base.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.base = model_base

    def forward(self, x):
        x = self.base.layer1(x)
        x = self.base.layer2(x)
        x = self.base.layer3(x)
        x = self.base.layer4(x)
        return x

class CNL(nn.Module):
    # 跨层注意力
    def __init__(self, high_dim, low_dim, flag=0):
        super(CNL, self).__init__()
        self.high_dim = high_dim
        self.low_dim = low_dim

        self.g = nn.Conv2d(self.low_dim, self.low_dim, kernel_size=1, stride=1, padding=0)
        self.theta = nn.Conv2d(self.high_dim, self.low_dim, kernel_size=1, stride=1, padding=0)
        if flag == 0:
            self.phi = nn.Conv2d(self.low_dim, self.low_dim, kernel_size=1, stride=1, padding=0)
            self.W = nn.Sequential(nn.Conv2d(self.low_dim, self.high_dim, kernel_size=1, stride=1, padding=0), nn.BatchNorm2d(high_dim),)
        else:
            self.phi = nn.Conv2d(self.low_dim, self.low_dim, kernel_size=1, stride=2, padding=0)
            self.W = nn.Sequential(nn.Conv2d(self.low_dim, self.high_dim, kernel_size=1, stride=2, padding=0), nn.BatchNorm2d(self.high_dim), )
        nn.init.constant_(self.W[1].weight, 0.0)
        nn.init.constant_(self.W[1].bias, 0.0)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x_h, x_l):
        B = x_h.size(0)
        g_x = self.g(x_l).view(B, self.low_dim, -1)

        theta_x = self.theta(x_h).view(B, self.low_dim, -1)
        phi_x = self.phi(x_l).view(B, self.low_dim, -1).permute(0, 2, 1)

        energy = torch.matmul(theta_x, phi_x)
        attention = energy / energy.size(-1)
        
        y = torch.matmul(attention, g_x)
        y = y.view(B, self.low_dim, *x_l.size()[2:])
        W_y = self.W(y)
        gate = self.sigmoid(W_y) # gating mechanism
        z = gate * W_y + (1 - gate) * x_h
        return z

class PNL(nn.Module):
    def __init__(self, high_dim, low_dim, reduc_ratio=2):
        super(PNL, self).__init__()
        self.high_dim = high_dim
        self.low_dim = low_dim
        self.reduc_ratio = reduc_ratio

        self.g = nn.Conv2d(self.low_dim, self.low_dim//self.reduc_ratio, kernel_size=1, stride=1, padding=0)
        self.theta = nn.Conv2d(self.high_dim, self.low_dim//self.reduc_ratio, kernel_size=1, stride=1, padding=0)
        self.phi = nn.Conv2d(self.low_dim, self.low_dim//self.reduc_ratio, kernel_size=1, stride=1, padding=0)

        self.W = nn.Sequential(nn.Conv2d(self.low_dim//self.reduc_ratio, self.high_dim, kernel_size=1, stride=1, padding=0), nn.BatchNorm2d(high_dim),)
        nn.init.constant_(self.W[1].weight, 0.0)
        nn.init.constant_(self.W[1].bias, 0.0)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x_h, x_l):
        B = x_h.size(0)
        g_x = self.g(x_l).view(B, self.low_dim, -1)
        g_x = g_x.permute(0, 2, 1)

        theta_x = self.theta(x_h).view(B, self.low_dim, -1)
        theta_x = theta_x.permute(0, 2, 1)
        
        phi_x = self.phi(x_l).view(B, self.low_dim, -1)

        energy = torch.matmul(theta_x, phi_x)
        attention = energy / energy.size(-1)

        y = torch.matmul(attention, g_x)
        y = y.permute(0, 2, 1).contiguous()
        y = y.view(B, self.low_dim//self.reduc_ratio, *x_h.size()[2:])
        W_y = self.W(y)
        gate = self.sigmoid(W_y) # gating mechanism
        z = gate * W_y + (1 - gate) * x_h
        return z

class SSM_block(nn.Module):
    def __init__(self, high_dim, low_dim, flag):
        super(SSM_block, self).__init__()

        self.CNL = CNL(high_dim, low_dim, flag)
        self.PNL = PNL(high_dim, low_dim)
    def forward(self, x, x0):
        z = self.CNL(x, x0)
        z = self.PNL(z, x0)
        return z


    # def forward(self, x):
    #     out = self.wh(x)
    #     if self.affine:
    #         out = out * self.gamma + self.beta + x
    #     return out

class embed_net(nn.Module):
    def __init__(self, class_num, arch='resnet50', graphw=1.0, theta1=0.0, edge=256,
             use_diagonal=False, max_depth=3, diagonal_threshold=0.5,
             simplified_diagonal=False, fusion_strategy='advanced',
             use_beta_distribution=False, dataset_name='SYSU-MM01', beta_params=None,
             # 跨模态对比学习参数
             enable_cross_modal=False, num_strips=4, temperature=0.07,
             contrastive_weight=0.5, projection_dim=128, num_heads=4,
             use_vectorized=True, gradient_checkpointing=False):
        super(embed_net, self).__init__()
        self.part_num = 4
        self.global_dim = 2048
        self.style_dim = 256

        # 跨模态对比学习参数
        self.enable_cross_modal = enable_cross_modal
        self.num_strips = num_strips
        self.contrastive_weight = contrastive_weight
        self.thermal_module = thermal_module(arch=arch)
        self.visible_module = visible_module(arch=arch)
        self.thermal_moduleA = thermal_moduleA(arch=arch)
        self.visible_moduleA = visible_moduleA(arch=arch)
        self.base_resnet = base_resnet(arch=arch)
        self.SSM1 = SSM_block(256, 64, 0)
        self.SSM2 = SSM_block(512, 256, 1)
        
        self.l2norm = Normalize(2)

        self.bottleneck2 = nn.BatchNorm1d(self.style_dim)#BN1D用于 [B, C] 的向量，线性层、池化后
        self.bottleneck2.bias.requires_grad_(False)  # no shift
        self.pattern_classifiers = nn.ModuleList([
            nn.Linear(self.style_dim, class_num, bias=False) for _ in range(self.part_num)
        ])
        for classifier in self.pattern_classifiers:
            classifier.apply(weights_init_classifier)
        
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1)) 

        self.activation = nn.Sigmoid()
        self.spatial_attention = nn.Conv2d(self.global_dim, self.part_num, kernel_size=1, stride=1, padding=0, bias=True)     
        self.pool_dim = self.global_dim + self.style_dim * self.part_num
        self.bottleneck = nn.BatchNorm1d(self.pool_dim)
        self.bottleneck.bias.requires_grad_(False)  # no shift
        self.classifier = nn.Linear(self.pool_dim, class_num, bias=False)
        self.bottleneck.apply(weights_init_kaiming)
        self.classifier.apply(weights_init_classifier)
        self.weight_sep = 0.2
        # 根据配置参数动态选择超图卷积类
        if use_beta_distribution and use_diagonal:
            # 同时启用Beta分布和对角索引 - 使用完整版本
            self.hypergraph = HypergraphConvDiag(
                in_features=self.style_dim,  # 输入特征维度
                out_features=self.style_dim,  # 输出特征维度
                features_height=24,  # 特征图高度 - 修正为实际尺寸
                features_width=12,   # 特征图宽度 - 修正为实际尺寸
                theta1=theta1,  # 稀疏化阈值参数
                edges=edge,  # 超边数量
                use_diagonal=use_diagonal,  # 使用传入的参数
                max_depth=max_depth,  # 使用传入的参数
                diagonal_threshold=diagonal_threshold,  # 使用传入的参数
                simplified_mode=simplified_diagonal,  # 使用传入的参数
                fusion_strategy=fusion_strategy,  # 使用传入的参数
                use_beta_distribution=use_beta_distribution,  # Beta分布开关
                dataset_name=dataset_name,  # 数据集名称
                beta_params=beta_params,  # 传递Beta分布参数
            )
        elif use_beta_distribution:
            # 仅启用Beta分布
            self.hypergraph = HypergraphConvBeta(
                in_features=self.style_dim,  # 输入特征维度
                out_features=self.style_dim,  # 输出特征维度
                features_height=24,  # 特征图高度 - 修正为实际尺寸
                features_width=12,   # 特征图宽度 - 修正为实际尺寸
                theta1=theta1,  # 稀疏化阈值参数
                edges=edge,  # 超边数量
                use_beta_distribution=use_beta_distribution,  # Beta分布开关
                dataset_name=dataset_name,  # 数据集名称
                beta_params=beta_params,  # 传递Beta分布参数
            )
        elif use_diagonal:
            # 仅启用对角索引
            self.hypergraph = HypergraphConvDiagonal(
                in_features=self.style_dim,  # 输入特征维度
                out_features=self.style_dim,  # 输出特征维度
                features_height=24,  # 特征图高度 - 修正为实际尺寸
                features_width=12,   # 特征图宽度 - 修正为实际尺寸
                theta1=theta1,  # 稀疏化阈值参数
                edges=edge,  # 超边数量
                use_diagonal=use_diagonal,  # 使用传入的参数
                max_depth=max_depth,  # 使用传入的参数
                diagonal_threshold=diagonal_threshold,  # 使用传入的参数
                simplified_mode=simplified_diagonal,  # 使用传入的参数
                fusion_strategy=fusion_strategy,  # 使用传入的参数
            )
        else:
            # 都不启用 - 使用基础超图卷积
            self.hypergraph = HypergraphConv(
                in_features=self.style_dim,  # 输入特征维度
                out_features=self.style_dim,  # 输出特征维度
                features_height=24,  # 特征图高度 - 修正为实际尺寸
                features_width=12,   # 特征图宽度 - 修正为实际尺寸
                theta1=theta1,  # 稀疏化阈值参数
                edges=edge,  # 超边数量
            )

        # self.graphw = graphw
        self.graphw = nn.Parameter(torch.tensor(graphw, requires_grad=True))
        self.conv_reduce = nn.Conv2d(in_channels=2048, out_channels=self.style_dim, kernel_size=1, stride=1, padding=0)
        self.bn_conv_reduce = nn.BatchNorm2d(self.style_dim)
        # BN2用于 [B, C, H, W] 的卷积特征图
        # self.relu = nn.ReLU(inplace=True)

        # 存储Beta分布和数据集参数
        self.use_beta_distribution = use_beta_distribution
        self.use_diagonal = use_diagonal
        self.dataset_name = dataset_name

        # 初始化跨模态对比学习模块
        if self.enable_cross_modal:
            self.cross_modal_contrastive = VectorizedCrossModalContrastive(
                feat_dim=self.style_dim,
                num_strips=num_strips,
                temperature=temperature,
                projection_dim=projection_dim,
                num_heads=num_heads,
                use_vectorized=use_vectorized,
                gradient_checkpointing=gradient_checkpointing
            )

    def _detect_modality_info(self, x1_1, x1_2, x2_1, x2_2, modal):
        """
        检测当前处理的模态信息
        Args:
            x1_1, x1_2: 可见光原图和增强图
            x2_1, x2_2: 红外原图和增强图
            modal: 0=可见光模式, 1=红外模式
        Returns:
            str: 模态类型
        """
        if modal == 0:  # 可见光模式
            # 检查是否为增强图（通过比较两个输入是否相同）
            if torch.allclose(x1_1, x1_2, rtol=1e-3):
                return 'visible_original'
            else:
                return 'visible_enhanced'
        else:  # 红外模式
            if torch.allclose(x2_1, x2_2, rtol=1e-3):
                return 'thermal_original'
            else:
                return 'thermal_enhanced'

    def forward(self, x1_1, x1_2, x2_1, x2_2, modal=0, identities=None):
        # 检测模态信息（用于Beta分布）
        modality_hint = None
        if self.use_beta_distribution:
            modality_hint = self._detect_modality_info(x1_1, x1_2, x2_1, x2_2, modal)

        if modal == 0: #four-stream feature extracter
            x1_1 = self.visible_module(x1_1)
            x2_1 = self.thermal_module(x2_1)
            x1_2 = self.visible_moduleA(x1_2)
            x2_2 = self.thermal_moduleA(x2_2)
            x = torch.cat((x1_1, x1_2,x2_1,x2_2), 0)
        elif modal == 1: #In the test mode, merge original features with data-augmented features
            x1_1 = self.visible_module(x1_1)       
            x1_2 = self.visible_moduleA(x1_2)
            x_mix=(x1_1+x1_2)/2
            x=x_mix

            #x = torch.cat((x1_1, x1_1), 0)
            #x = torch.cat((x1_2, x1_2), 0)
        elif modal == 2:
            x2_1 = self.thermal_module(x2_1)
            x2_2 = self.thermal_moduleA(x2_2)
            x_mix=(x2_1+x2_2)/2
            x=x_mix

            #x = torch.cat((x2_1, x2_1), 0)
            #x = torch.cat((x2_2, x2_2), 0)

        # shared block
        x_low = x
        x = self.base_resnet.base.layer1(x) 
        x = self.SSM1(x, x_low)
        x_low = x
        x = self.base_resnet.base.layer2(x)
        x = self.SSM2(x, x_low)
        x = self.base_resnet.base.layer3(x)  
        x = self.base_resnet.base.layer4(x)
        global_feat = x
        # global_feat.shape: torch.Size([64, 2048, 24, 12])
        b, _, h, w = x.shape

        #Generate part_num types of semantic style maps
        masks = self.spatial_attention(global_feat) 
        # masks.shape: torch.Size([64, 4, 24, 12])
        masks = self.activation(masks) # activation for maps
        feats = []
        feat_logit_styles = []

        # 存储跨模态对比学习所需的特征
        cross_modal_feats = [] if self.enable_cross_modal and modal == 0 and self.training else None

        for i in range(self.part_num): #for each style map
            mask = masks[:, i:i+1, :, :] #获取第i个风格掩码
            x = mask*global_feat
            x = self.conv_reduce(x) # use convolution to reduce the dimension
            x_bn = self.bn_conv_reduce(x)  # add a BN
            # print('--------x.shape:', x.shape)

            # 根据超图卷积类型选择合适的调用方式
            if hasattr(self.hypergraph, 'use_beta_distribution') and self.hypergraph.use_beta_distribution:
                # 支持Beta分布的类需要传递模态信息
                feat_graph = self.graphw * self.hypergraph(x_bn, modality_hint)
            else:
                # 其他类只需要传递特征
                feat_graph = self.graphw * self.hypergraph(x_bn)

            # 保存特征用于可视化分析
            if (not self.training and hasattr(self, '_capture_features') and
                self._capture_features and FEATURE_VIS_AVAILABLE):
                save_features_for_visualization(x_bn, feat_graph, i, identities)

            # classification for each style with a standalone linear classifier
            x_pool = self.avgpool(x_bn) # pooling
            x_pool = x_pool.view(x_pool.size(0), x_pool.size(1))
            feat = self.bottleneck2(x_pool) # BN
            feat_logit = self.pattern_classifiers[i](feat)
            # [B, class_num]
            feat_logit_styles.append(feat_logit)

            # 存储跨模态特征（使用超图卷积后的特征）
            if cross_modal_feats is not None:
                cross_modal_feats.append(feat_graph.clone())

            # 池化超图卷积后的特征
            feat = F.avg_pool2d(feat_graph, feat_graph.size()[2:]) # pooling
            feat = feat.view(feat.size(0), -1)
            feats.append(feat)

        global_feat = F.avg_pool2d(global_feat, global_feat.size()[2:])
        global_feat = global_feat.view(global_feat.size(0), -1)
        feats.append(global_feat)

        # concatenate pooled style features with the pooled global feature
        feats = torch.cat(feats, 1)
        #feats 形状为 [B, 3072]
        x_pool = feats            
  
        # 跨模态对比学习
        contrastive_loss = 0
        if self.enable_cross_modal and modal == 0 and self.training and identities is not None:
            contrastive_loss = self._apply_cross_modal_contrastive(cross_modal_feats, identities)

        if self.training:
            masks = masks.view(b, self.part_num, w*h)
            # 对每个样本，计算 4 个风格掩码之间的内积相似度矩阵
            loss_reg = torch.bmm(masks, masks.permute(0, 2, 1))
            # 只计算上三角矩阵的元素,掩码正交损失，希望不同 style 掩码之间的响应位置不重叠
            loss_reg = torch.triu(loss_reg, diagonal = 1).sum() / (b * self.part_num * (self.part_num - 1) / 2)
            if loss_reg != 0 :
                loss_sep = loss_reg.float() * self.weight_sep
            else:
                loss_sep = 0
        
        feat = self.bottleneck(x_pool)
        feat_logit = self.classifier(feat)
        
        if self.training:
            # 特征拼接后输出,特征拼接后输出的logit,正则化损失+对比损失，输出列表
            total_loss = loss_sep + self.contrastive_weight * contrastive_loss
            return x_pool, feat_logit, total_loss, feat_logit_styles
        else:
            return self.l2norm(x_pool), self.l2norm(feat)

    def _apply_cross_modal_contrastive(self, cross_modal_feats, identities):
        """
        应用跨模态对比学习
        Args:
            cross_modal_feats: list of [B, C, H, W] 每个part的特征
            identities: [B] 身份标签
        Returns:
            contrastive_loss: scalar
        """
        if not cross_modal_feats or len(cross_modal_feats) == 0:
            return torch.tensor(0.0, device=identities.device, requires_grad=True)

        # 假设batch中包含4个模态的数据，按顺序排列
        batch_size = cross_modal_feats[0].size(0)
        if batch_size % 4 != 0:
            # 如果batch size不是4的倍数，跳过跨模态对比学习
            return torch.tensor(0.0, device=identities.device, requires_grad=True)

        true_batch_size = batch_size // 4
        total_contrastive_loss = 0

        # 对每个part分别进行跨模态对比学习
        for part_feat in cross_modal_feats:
            # 重新组织特征：[B*4, C, H, W] -> [B, 4, C, H, W]
            part_feat_reshaped = part_feat.view(true_batch_size, 4, *part_feat.shape[1:])

            # 创建水平条带
            modal_strips = create_horizontal_strips_batch(
                part_feat_reshaped, self.num_strips
            )  # [B, 4, num_strips, C]

            # 获取对应的身份标签并确保在正确的设备上
            part_identities = identities[:true_batch_size].to(part_feat.device)

            # 应用跨模态对比学习
            _, contrastive_loss, _ = self.cross_modal_contrastive(
                modal_strips, part_identities
            )

            total_contrastive_loss += contrastive_loss

        return total_contrastive_loss / len(cross_modal_feats)
