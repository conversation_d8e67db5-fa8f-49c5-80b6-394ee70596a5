import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.checkpoint import checkpoint
'''SYSU-MM01: 使用默认配置
RegDB: 降低参数 (num_strips=3, temperature=0.05)
LLCM: 增强对比 (contrastive_weight=0.7, temperature=0.1)
内存不足: 启用gradient_checkpointing
训练不稳定: 使用渐进式权重调整'''

class VectorizedCrossModalContrastive(nn.Module):
    """
    优化的跨模态对比学习模块
    - 向量化计算减少循环
    - 支持梯度检查点节省内存
    - 基于人体拓扑的水平条带划分
    """
    def __init__(self, feat_dim=256, num_strips=4, temperature=0.07, 
                 projection_dim=128, num_heads=4, use_vectorized=True,
                 gradient_checkpointing=False):
        super().__init__()
        self.num_strips = num_strips
        self.temperature = temperature
        self.projection_dim = projection_dim
        self.use_vectorized = use_vectorized
        self.gradient_checkpointing = gradient_checkpointing
        
        # 向量化的投影头 - 一次性处理所有条带
        self.projection_head = nn.Sequential(
            nn.Linear(feat_dim, feat_dim),
            nn.ReLU(inplace=True),
            nn.Linear(feat_dim, projection_dim),
            nn.LayerNorm(projection_dim)
        )
        
        # 跨模态融合注意力
        self.cross_modal_attention = nn.MultiheadAttention(
            embed_dim=projection_dim,
            num_heads=num_heads,
            batch_first=True,
            dropout=0.1
        )
        
        # 条带位置编码
        self.strip_position_embedding = nn.Parameter(
            torch.randn(num_strips, projection_dim) * 0.02
        )
        
        # 模态类型编码
        self.modality_embedding = nn.Parameter(
            torch.randn(4, projection_dim) * 0.02  # 4个模态
        )
        
    def create_horizontal_strips_vectorized(self, feat_map):
        """
        向量化的水平条带创建
        Args:
            feat_map: [B, C, H, W]
        Returns:
            strips: [B, num_strips, C]
        """
        B, C, H, W = feat_map.shape
        
        # 计算每个条带的高度
        strip_heights = [H // self.num_strips] * self.num_strips
        # 处理不能整除的情况
        remainder = H % self.num_strips
        for i in range(remainder):
            strip_heights[i] += 1
            
        # 向量化创建条带
        strips = []
        start_h = 0
        for strip_height in strip_heights:
            end_h = start_h + strip_height
            strip = feat_map[:, :, start_h:end_h, :]  # [B, C, strip_h, W]
            # 全局平均池化
            strip_feat = F.adaptive_avg_pool2d(strip, (1, 1)).squeeze(-1).squeeze(-1)  # [B, C]
            strips.append(strip_feat)
            start_h = end_h
            
        return torch.stack(strips, dim=1)  # [B, num_strips, C]
    
    def compute_contrastive_loss_vectorized(self, projected_feats, identities):
        """
        向量化的对比损失计算
        Args:
            projected_feats: [B, 4, num_strips, projection_dim]
            identities: [B]
        Returns:
            loss: scalar
        """
        B, num_modalities, num_strips, feat_dim = projected_feats.shape
        
        # 重塑为 [B*4*num_strips, projection_dim]
        feats_flat = projected_feats.view(-1, feat_dim)
        
        # 扩展身份标签 [B] -> [B*4*num_strips]
        ids_expanded = identities.unsqueeze(1).unsqueeze(2).expand(-1, num_modalities, num_strips)
        ids_flat = ids_expanded.contiguous().view(-1)
        
        # 计算相似度矩阵 - 向量化
        sim_matrix = torch.matmul(feats_flat, feats_flat.T) / self.temperature
        
        # 创建正样本掩码 - 同一身份的不同模态或条带
        pos_mask = (ids_flat.unsqueeze(0) == ids_flat.unsqueeze(1)).float()
        
        # 移除自身对角线
        pos_mask.fill_diagonal_(0)
        
        # 向量化计算InfoNCE损失
        exp_sim = torch.exp(sim_matrix)
        
        # 计算分子：正样本相似度之和
        pos_sim_sum = (exp_sim * pos_mask).sum(dim=1)
        
        # 计算分母：所有样本相似度之和（除了自身）
        neg_sim_sum = exp_sim.sum(dim=1) - torch.diag(exp_sim)
        
        # 避免除零和log(0)
        valid_mask = pos_sim_sum > 0
        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=feats_flat.device, requires_grad=True)
            
        # 计算损失
        loss = -torch.log(pos_sim_sum[valid_mask] / (pos_sim_sum[valid_mask] + neg_sim_sum[valid_mask] + 1e-8))
        
        return loss.mean()
    
    def forward_with_checkpoint(self, modal_strips, identities):
        """使用梯度检查点的前向传播"""
        return checkpoint(self._forward_impl, modal_strips, identities, use_reentrant=False)
    
    def _forward_impl(self, modal_strips, identities):
        """实际的前向传播实现"""
        B, num_modalities, num_strips, feat_dim = modal_strips.shape
        
        # 重塑为 [B*4*num_strips, feat_dim] 进行批量投影
        modal_strips_flat = modal_strips.view(-1, feat_dim)
        
        # 批量投影到对比学习空间
        projected_flat = self.projection_head(modal_strips_flat)  # [B*4*num_strips, projection_dim]
        
        # 重塑回原始形状
        projected_feats = projected_flat.view(B, num_modalities, num_strips, self.projection_dim)
        
        # 添加位置和模态编码
        for strip_idx in range(num_strips):
            projected_feats[:, :, strip_idx, :] += self.strip_position_embedding[strip_idx]
            
        for modal_idx in range(num_modalities):
            projected_feats[:, modal_idx, :, :] += self.modality_embedding[modal_idx]
        
        # 计算对比损失
        contrastive_loss = self.compute_contrastive_loss_vectorized(projected_feats, identities)
        
        # 跨模态注意力融合
        # 重塑为 [B, 4*num_strips, projection_dim] 进行注意力计算
        projected_reshaped = projected_feats.view(B, -1, self.projection_dim)
        
        enhanced_feat, attention_weights = self.cross_modal_attention(
            projected_reshaped,  # query
            projected_reshaped,  # key
            projected_reshaped   # value
        )
        
        # 重塑回 [B, 4, num_strips, projection_dim] 并聚合模态维度
        enhanced_feat = enhanced_feat.view(B, num_modalities, num_strips, self.projection_dim)
        enhanced_feat = enhanced_feat.mean(dim=1)  # [B, num_strips, projection_dim]
        
        return enhanced_feat, contrastive_loss, attention_weights
    
    def forward(self, modal_strips, identities):
        """
        前向传播
        Args:
            modal_strips: [B, 4, num_strips, feat_dim] 
                         # 4个模态的条带特征
            identities: [B] 身份标签
        Returns:
            enhanced_strips: [B, num_strips, projection_dim]
            contrastive_loss: scalar
            attention_weights: [B, 4*num_strips, 4*num_strips]
        """
        if self.gradient_checkpointing and self.training:
            return self.forward_with_checkpoint(modal_strips, identities)
        else:
            return self._forward_impl(modal_strips, identities)


def create_horizontal_strips_batch(feat_maps, num_strips=4):
    """
    批量创建水平条带 - 优化版本
    Args:
        feat_maps: list of [B, C, H, W] 或 [B*4, C, H, W]
    Returns:
        strips: [B, 4, num_strips, C]
    """
    if isinstance(feat_maps, list):
        # 如果是列表，堆叠成一个tensor
        feat_maps = torch.stack(feat_maps, dim=1)  # [B, 4, C, H, W]
    
    B, num_modalities, C, H, W = feat_maps.shape
    
    # 计算条带高度
    strip_height = H // num_strips
    
    # 向量化处理所有条带
    strips = []
    for i in range(num_strips):
        start_h = i * strip_height
        end_h = (i + 1) * strip_height if i < num_strips - 1 else H
        
        # 提取条带 [B, 4, C, strip_h, W]
        strip = feat_maps[:, :, :, start_h:end_h, :]
        
        # 全局平均池化 [B, 4, C]
        strip_feat = F.adaptive_avg_pool2d(
            strip.view(-1, C, end_h - start_h, W), (1, 1)
        ).view(B, num_modalities, C)
        
        strips.append(strip_feat)
    
    # 堆叠所有条带 [B, 4, num_strips, C]
    return torch.stack(strips, dim=2)
