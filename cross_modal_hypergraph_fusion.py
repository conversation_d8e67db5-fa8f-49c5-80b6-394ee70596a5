#!/usr/bin/env python3
"""
跨模态超图融合模块
通过超图结构实现样本间的跨模态特征融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class CrossModalHypergraphFusion(nn.Module):
    """
    跨模态超图融合模块
    
    核心思想：
    1. 将batch中的样本按身份分组
    2. 同一身份的不同模态通过超边连接
    3. 通过超图卷积实现样本间信息融合
    4. 使用水平条带降低计算复杂度
    """
    
    def __init__(self, feat_dim, num_strips=4, fusion_dim=256, num_heads=4):
        super(CrossModalHypergraphFusion, self).__init__()
        
        self.feat_dim = feat_dim
        self.num_strips = num_strips
        self.fusion_dim = fusion_dim
        self.num_heads = num_heads
        
        # 特征投影层
        self.feature_projection = nn.Linear(feat_dim, fusion_dim)
        
        # 超图注意力机制
        self.hypergraph_attention = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=num_heads,
            batch_first=True
        )
        
        # 融合后的特征变换
        self.fusion_transform = nn.Sequential(
            nn.Linear(fusion_dim, fusion_dim),
            nn.ReLU(),
            nn.Linear(fusion_dim, feat_dim)
        )
        
        # 残差连接的权重
        self.residual_weight = nn.Parameter(torch.tensor(0.1))
        
    def create_horizontal_strips(self, features):
        """
        创建水平条带以降低计算复杂度
        Args:
            features: [4B, C, H, W] 所有模态的特征
        Returns:
            strips: [4B, num_strips, C] 条带特征
        """
        B_total, C, H, W = features.shape
        
        # 计算每个条带的高度
        strip_height = H // self.num_strips
        
        strips = []
        for i in range(self.num_strips):
            start_h = i * strip_height
            end_h = (i + 1) * strip_height if i < self.num_strips - 1 else H
            
            # 提取条带并进行全局平均池化
            strip = features[:, :, start_h:end_h, :]  # [4B, C, strip_h, W]
            strip = F.adaptive_avg_pool2d(strip, (1, 1)).squeeze(-1).squeeze(-1)  # [4B, C]
            strips.append(strip)
        
        strips = torch.stack(strips, dim=1)  # [4B, num_strips, C]
        return strips
    
    def build_hypergraph_structure(self, identities, batch_size):
        """
        构建超图结构
        Args:
            identities: [B] 身份标签
            batch_size: B
        Returns:
            hyperedge_matrix: [4B, num_hyperedges] 超边关联矩阵
            hyperedge_weights: [num_hyperedges] 超边权重
        """
        device = identities.device
        
        # 为每个身份创建超边
        unique_ids = torch.unique(identities)
        num_hyperedges = len(unique_ids)
        
        # 初始化超边关联矩阵
        hyperedge_matrix = torch.zeros(4 * batch_size, num_hyperedges, device=device)
        hyperedge_weights = torch.ones(num_hyperedges, device=device)
        
        for i, identity in enumerate(unique_ids):
            # 找到属于当前身份的样本索引
            identity_mask = (identities == identity)
            identity_indices = torch.where(identity_mask)[0]
            
            # 为每个身份样本的4个模态建立超边连接
            for idx in identity_indices:
                # 4个模态的索引
                vis_orig_idx = idx
                vis_aug_idx = idx + batch_size
                ir_orig_idx = idx + 2 * batch_size
                ir_aug_idx = idx + 3 * batch_size
                
                # 连接到当前身份的超边
                hyperedge_matrix[vis_orig_idx, i] = 1.0
                hyperedge_matrix[vis_aug_idx, i] = 1.0
                hyperedge_matrix[ir_orig_idx, i] = 1.0
                hyperedge_matrix[ir_aug_idx, i] = 1.0
            
            # 根据该身份的样本数量设置超边权重
            num_samples_in_identity = len(identity_indices)
            if num_samples_in_identity > 1:
                hyperedge_weights[i] = 1.0 / num_samples_in_identity  # 归一化权重
        
        return hyperedge_matrix, hyperedge_weights
    
    def hypergraph_convolution(self, node_features, hyperedge_matrix, hyperedge_weights):
        """
        超图卷积操作
        Args:
            node_features: [4B, num_strips, fusion_dim] 节点特征
            hyperedge_matrix: [4B, num_hyperedges] 超边关联矩阵
            hyperedge_weights: [num_hyperedges] 超边权重
        Returns:
            fused_features: [4B, num_strips, fusion_dim] 融合后的特征
        """
        num_nodes, num_strips, feat_dim = node_features.shape
        num_hyperedges = hyperedge_matrix.shape[1]
        
        # 对每个条带分别进行超图卷积
        fused_strips = []
        
        for strip_idx in range(num_strips):
            strip_features = node_features[:, strip_idx, :]  # [4B, fusion_dim]
            
            # 步骤1：节点到超边的聚合
            # hyperedge_features = H^T * X
            hyperedge_features = torch.mm(hyperedge_matrix.t(), strip_features)  # [num_hyperedges, fusion_dim]
            
            # 应用超边权重
            weighted_hyperedge_features = hyperedge_features * hyperedge_weights.unsqueeze(1)
            
            # 步骤2：超边到节点的传播
            # fused_features = H * hyperedge_features
            fused_strip_features = torch.mm(hyperedge_matrix, weighted_hyperedge_features)  # [4B, fusion_dim]
            
            fused_strips.append(fused_strip_features)
        
        fused_features = torch.stack(fused_strips, dim=1)  # [4B, num_strips, fusion_dim]
        return fused_features
    
    def forward(self, features, identities):
        """
        前向传播
        Args:
            features: [4B, C, H, W] 所有模态的特征
            identities: [B] 身份标签
        Returns:
            enhanced_features: [4B, C, H, W] 增强后的特征
            fusion_loss: scalar 融合损失（可选）
        """
        B_total, C, H, W = features.shape
        batch_size = B_total // 4
        
        # 1. 创建水平条带
        strips = self.create_horizontal_strips(features)  # [4B, num_strips, C]
        
        # 2. 特征投影
        projected_strips = self.feature_projection(strips)  # [4B, num_strips, fusion_dim]
        
        # 3. 构建超图结构
        hyperedge_matrix, hyperedge_weights = self.build_hypergraph_structure(identities, batch_size)
        
        # 4. 超图卷积融合
        fused_strips = self.hypergraph_convolution(projected_strips, hyperedge_matrix, hyperedge_weights)
        
        # 5. 注意力增强（可选）
        # 将条带维度作为序列长度进行自注意力
        fused_strips_reshaped = fused_strips.view(-1, self.num_strips, self.fusion_dim)  # [4B, num_strips, fusion_dim]
        attended_strips, _ = self.hypergraph_attention(fused_strips_reshaped, fused_strips_reshaped, fused_strips_reshaped)
        
        # 6. 特征变换回原始维度
        enhanced_strips = self.fusion_transform(attended_strips)  # [4B, num_strips, C]
        
        # 7. 重构为原始特征图格式
        enhanced_features = self.reconstruct_feature_maps(enhanced_strips, features.shape)
        
        # 8. 残差连接
        enhanced_features = features + self.residual_weight * enhanced_features
        
        # 9. 计算融合损失（鼓励同身份特征相似）
        fusion_loss = self.compute_fusion_loss(enhanced_strips, identities, batch_size)
        
        return enhanced_features, fusion_loss
    
    def reconstruct_feature_maps(self, strips, original_shape):
        """
        将条带特征重构为原始特征图格式
        Args:
            strips: [4B, num_strips, C] 条带特征
            original_shape: (4B, C, H, W) 原始形状
        Returns:
            reconstructed: [4B, C, H, W] 重构的特征图
        """
        B_total, C, H, W = original_shape
        
        # 将条带特征扩展到空间维度
        reconstructed = torch.zeros(B_total, C, H, W, device=strips.device)
        
        strip_height = H // self.num_strips
        
        for i in range(self.num_strips):
            start_h = i * strip_height
            end_h = (i + 1) * strip_height if i < self.num_strips - 1 else H
            
            # 将条带特征广播到对应的空间区域
            strip_feat = strips[:, i, :].unsqueeze(-1).unsqueeze(-1)  # [4B, C, 1, 1]
            reconstructed[:, :, start_h:end_h, :] = strip_feat.expand(-1, -1, end_h - start_h, W)
        
        return reconstructed
    
    def compute_fusion_loss(self, enhanced_strips, identities, batch_size):
        """
        计算融合损失，鼓励同身份的不同模态特征相似
        """
        loss = 0.0
        count = 0
        
        # 对每个身份计算内部一致性损失
        unique_ids = torch.unique(identities)
        
        for identity in unique_ids:
            identity_mask = (identities == identity)
            identity_indices = torch.where(identity_mask)[0]
            
            if len(identity_indices) < 2:
                continue  # 跳过只有一个样本的身份
            
            # 获取该身份所有模态的特征
            identity_features = []
            for idx in identity_indices:
                # 4个模态的特征
                vis_orig = enhanced_strips[idx]  # [num_strips, C]
                vis_aug = enhanced_strips[idx + batch_size]
                ir_orig = enhanced_strips[idx + 2 * batch_size]
                ir_aug = enhanced_strips[idx + 3 * batch_size]
                
                identity_features.extend([vis_orig, vis_aug, ir_orig, ir_aug])
            
            # 计算该身份内部特征的一致性损失
            if len(identity_features) > 1:
                identity_features = torch.stack(identity_features)  # [num_modalities, num_strips, C]
                # 使用余弦相似度损失
                identity_features_flat = identity_features.view(identity_features.size(0), -1)
                identity_features_norm = F.normalize(identity_features_flat, p=2, dim=1)
                
                # 计算两两相似度
                similarity_matrix = torch.mm(identity_features_norm, identity_features_norm.t())
                
                # 鼓励高相似度（损失 = 1 - 平均相似度）
                mask = torch.eye(similarity_matrix.size(0), device=similarity_matrix.device) == 0
                avg_similarity = similarity_matrix[mask].mean()
                loss += (1.0 - avg_similarity)
                count += 1
        
        return loss / max(count, 1)


def test_cross_modal_hypergraph_fusion():
    """测试跨模态超图融合模块"""
    print("=== 测试跨模态超图融合模块 ===")
    
    # 模拟数据
    batch_size = 4
    feat_dim = 256
    H, W = 18, 9
    
    # 创建模拟特征
    features = torch.randn(4 * batch_size, feat_dim, H, W)
    identities = torch.tensor([1, 2, 1, 3])  # 身份1有2个样本，身份2和3各1个
    
    print(f"输入特征形状: {features.shape}")
    print(f"身份标签: {identities}")
    
    # 创建融合模块
    fusion_module = CrossModalHypergraphFusion(
        feat_dim=feat_dim,
        num_strips=4,
        fusion_dim=128,
        num_heads=4
    )
    
    # 前向传播
    enhanced_features, fusion_loss = fusion_module(features, identities)
    
    print(f"增强特征形状: {enhanced_features.shape}")
    print(f"融合损失: {fusion_loss.item():.6f}")
    
    # 验证特征变化
    feature_change = torch.norm(enhanced_features - features).item()
    print(f"特征变化程度: {feature_change:.6f}")
    
    print("✅ 跨模态超图融合模块测试完成")


if __name__ == "__main__":
    test_cross_modal_hypergraph_fusion()
