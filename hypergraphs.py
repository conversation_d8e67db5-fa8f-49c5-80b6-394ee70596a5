import torch
import torch.nn as nn
import torch.nn.functional as F


class ModalityDetector:
    """模态检测器，用于自动识别输入数据的模态类型"""

    @staticmethod
    def detect_modality_type(x, modality_hint=None):
        """
        基于模态提示检测模态类型
        Args:
            x: [B, C, H, W] 输入特征
            modality_hint: str, 模态提示信息 ('visible_original', 'visible_enhanced', 'thermal_original', 'thermal_enhanced')
        Returns:
            str: 模态类型
        """
        # 如果有明确的模态提示，直接使用
        if modality_hint and modality_hint in ['visible_original', 'visible_enhanced', 'thermal_original', 'thermal_enhanced']:
            return modality_hint

        # 如果没有提示或提示无效，基于特征统计判断
        intensity_mean = torch.mean(x).item()
        intensity_std = torch.std(x).item()

        # 基于统计特性判断
        if intensity_mean > 0.6:
            result = 'visible_original'
        elif intensity_mean > 0.4:
            result = 'thermal_original'
        elif intensity_std > 0.3:
            result = 'visible_enhanced'
        else:
            result = 'thermal_enhanced'

        return result


class BetaDistributionM(nn.Module):
    """基于Beta分布的概率化超边连接矩阵生成器"""

    def __init__(self, in_channels, edges, vertices, dataset_name='SYSU-MM01', beta_params=None):
        super().__init__()
        self.in_channels = in_channels
        self.edges = edges
        self.vertices = vertices
        self.dataset_name = dataset_name
        self.external_beta_params = beta_params  # 外部传入的Beta参数

        # Beta分布参数网络
        self.alpha_net = nn.Conv2d(in_channels, edges, kernel_size=1, stride=1, padding=0)
        self.beta_net = nn.Conv2d(in_channels, edges, kernel_size=1, stride=1, padding=0)

        # 特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, kernel_size=1, stride=1, padding=0)
        )

        # 模态感知调制网络
        self.modality_modulator = nn.ModuleDict({
            'visible_original': nn.Linear(edges, edges),
            'visible_enhanced': nn.Linear(edges, edges),
            'thermal_original': nn.Linear(edges, edges),
            'thermal_enhanced': nn.Linear(edges, edges)
        })

        # 数据集特定参数 - 优先使用外部传入的参数
        self.dataset_params = self._get_dataset_params()

        # 初始化权重
        self._initialize_weights()

    def _get_dataset_params(self):
        """获取数据集特定的Beta分布参数"""
        # 如果有外部传入的参数，优先使用
        if self.external_beta_params is not None:
            return self.external_beta_params

        # 否则使用默认参数
        params = {
            'SYSU-MM01': {
                'visible_original': {'alpha_base': 2.5, 'beta_base': 1.2},
                'visible_enhanced': {'alpha_base': 2.0, 'beta_base': 1.5},
                'thermal_original': {'alpha_base': 2.0, 'beta_base': 1.3},
                'thermal_enhanced': {'alpha_base': 2.8, 'beta_base': 1.0}
            },
            'LLCM': {
                'visible_original': {'alpha_base': 3.0, 'beta_base': 1.0},
                'visible_enhanced': {'alpha_base': 2.2, 'beta_base': 1.4},
                'thermal_original': {'alpha_base': 2.5, 'beta_base': 1.1},
                'thermal_enhanced': {'alpha_base': 3.2, 'beta_base': 0.8}
            },
            'REGDB': {
                'visible_original': {'alpha_base': 2.8, 'beta_base': 1.1},
                'visible_enhanced': {'alpha_base': 2.3, 'beta_base': 1.3},
                'thermal_original': {'alpha_base': 2.6, 'beta_base': 1.0},
                'thermal_enhanced': {'alpha_base': 3.0, 'beta_base': 0.9}
            }
        }
        default_params = params.get(self.dataset_name, params['SYSU-MM01'])
        print(f"使用默认Beta分布参数 (数据集: {self.dataset_name}): {default_params}")
        return default_params

    def _initialize_weights(self):
        """初始化网络权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)

    def forward(self, x, modality_hint=None):
        """
        生成Beta分布的概率连接矩阵
        Args:
            x: [B, C, H, W] 输入特征
            modality_hint: str, 模态提示信息
        Returns:
            M_prob: [B, V, E] 概率化连接矩阵
        """
        B, _, _, _ = x.shape

        # 检测模态类型
        modality_type = ModalityDetector.detect_modality_type(x, modality_hint)

        # 特征增强
        enhanced_x = self.feature_enhancer(x)

        # 生成Beta分布参数
        alpha_raw = self.alpha_net(enhanced_x)  # [B, edges, H, W]
        beta_raw = self.beta_net(enhanced_x)    # [B, edges, H, W]

        # 确保参数 > 0，并应用数据集特定的基础参数
        base_params = self.dataset_params[modality_type]
        alpha_base = base_params['alpha_base']
        beta_base = base_params['beta_base']

        alpha = F.softplus(alpha_raw) * alpha_base + 0.5
        beta = F.softplus(beta_raw) * beta_base + 0.5

        # 模态感知调制
        alpha, beta = self._apply_modality_modulation(alpha, beta, modality_type)

        # 数据集特定调整
        alpha, beta = self._apply_dataset_adjustment(alpha, beta, modality_type)

        # 重塑维度 [B, edges, H, W] -> [B, V, E]
        alpha = alpha.permute(0, 2, 3, 1).contiguous().view(B, -1, self.edges)
        beta = beta.permute(0, 2, 3, 1).contiguous().view(B, -1, self.edges)

        # 计算Beta分布期望作为连接概率
        # E[Beta(α,β)] = α/(α+β)
        prob_matrix = alpha / (alpha + beta)

        # 后处理
        prob_matrix = self._postprocess_probability(prob_matrix, modality_type)

        return prob_matrix

    def _apply_modality_modulation(self, alpha, beta, modality_type):
        """应用模态感知调制"""
        B, E, _, _ = alpha.shape

        # 计算全局特征用于调制
        alpha_global = F.adaptive_avg_pool2d(alpha, (1, 1)).view(B, E)  # [B, E]
        beta_global = F.adaptive_avg_pool2d(beta, (1, 1)).view(B, E)    # [B, E]

        # 模态特定调制
        if modality_type in self.modality_modulator:
            alpha_modulated = self.modality_modulator[modality_type](alpha_global)
            beta_modulated = self.modality_modulator[modality_type](beta_global)

            # 应用调制因子
            alpha_factor = torch.sigmoid(alpha_modulated).view(B, E, 1, 1)
            beta_factor = torch.sigmoid(beta_modulated).view(B, E, 1, 1)

            alpha = alpha * (0.5 + alpha_factor)
            beta = beta * (0.5 + beta_factor)

        return alpha, beta

    def _apply_dataset_adjustment(self, alpha, beta, modality_type):
        """应用数据集特定调整"""
        if self.dataset_name == 'SYSU-MM01':
            # SYSU数据集光照变化大，增加适应性
            if 'thermal' in modality_type:
                # 红外模态增强稳定性
                alpha = alpha * 1.1
                beta = beta * 0.95
            else:
                # 可见光模态增加鲁棒性
                alpha = alpha * (0.9 + 0.2 * torch.sigmoid(alpha - 2.0))

        elif self.dataset_name == 'LLCM':
            # LLCM数据集模态差异大，增强对比
            if 'thermal' in modality_type:
                # 热成像增强连接强度
                alpha = alpha * 1.2
                beta = beta * 0.9
            else:
                # 可见光适度保守
                alpha = alpha * 0.95
                beta = beta * 1.05

        elif self.dataset_name == 'RegDB':
            # RegDB数据集质量稳定，保持参数稳定
            if 'enhanced' in modality_type:
                # 增强模态略微提升
                alpha = alpha * 1.05
                beta = beta * 0.98

        return alpha, beta

    def _postprocess_probability(self, prob_matrix, modality_type):
        """概率矩阵后处理"""
        # 基本范围限制
        prob_matrix = torch.clamp(prob_matrix, 0.05, 0.95)

        # 模态特定后处理
        if modality_type == 'thermal_enhanced':
            # 热成像增强模态：增强高概率连接
            prob_matrix = torch.where(prob_matrix > 0.6,
                                    prob_matrix * 1.1,
                                    prob_matrix)
        elif modality_type == 'visible_enhanced':
            # 可见光增强模态：平滑处理
            prob_matrix = 0.9 * prob_matrix + 0.1 * torch.mean(prob_matrix, dim=-1, keepdim=True)

        # 数据集特定后处理
        if self.dataset_name == 'LLCM' and 'thermal' in modality_type:
            # LLCM热成像进一步增强
            prob_matrix = torch.clamp(prob_matrix * 1.05, 0.05, 0.95)

        return prob_matrix
'''
假设输入尺寸：[batch, 1024, 18, 9]

phi_conv: [B,1024,18,9] → [B,128,18,9]

phi变形: [B,128,18,9] → [B,162,128] (V=18×9=162)

A计算: [B,1024,18,9] → [B,1024,1,1] → [B,128,1,1] → [B,128,128]

M_conv: [B,1024,18,9] → [B,256,18,9] → [B,162,256]

H计算: [B,162,128]×[B,128,128]×([B,128,162]×[B,162,256]) → [B,162,256]

卷积输出: [B,162,1024] → [B,162,1024] → [B,1024,162] → [B,1024,18,9]
'''

class HypergraphConv(nn.Module):
    def __init__(
            self,
            in_features=1024,
            out_features=1024,
            features_height=18,
            features_width=9,
            edges=256,
            filters=128,
            apply_bias=True,
            theta1 = 0.0
    ):
        super().__init__()

        self.in_features = in_features
        self.out_features = out_features
        self.features_height = features_height
        self.features_width = features_width
        self.vertices = self.features_height * self.features_width
        self.edges = edges
        self.apply_bias = apply_bias
        self.filters = filters #中间特征维度
        self.theta1 = theta1 #阈值参数，用于稀疏化
        #节点特征,边权重,超边关联矩阵
        self.phi_conv = nn.Conv2d(self.in_features, self.filters, kernel_size=1, stride=1, padding=0)
        self.A_conv = nn.Conv2d(self.in_features, self.filters, kernel_size=1, stride=1, padding=0)
        self.M_conv = nn.Conv2d(self.in_features, self.edges, kernel_size=7, stride=1, padding=3)

        self.weight_2 = nn.Parameter(torch.empty(self.in_features, self.out_features))
        nn.init.xavier_normal_(self.weight_2)

        if apply_bias:
            self.bias_2 = nn.Parameter(torch.empty(1, self.out_features))
            nn.init.xavier_normal_(self.bias_2)

    def forward(self, x):
        # Get input dimensions
        B, _, H, W = x.size()
        
        # 获取顶点表示 phi
        phi = self.phi_conv(x)  # [B, filters, H, W]
        phi = phi.permute(0, 2, 3, 1).contiguous()  # [B, H, W, filters]
        phi = phi.view(-1, self.vertices, self.filters)  # [B, V, filters]
        # 构造超边权重 A（注意：batch-wise）
        A = F.avg_pool2d(x, kernel_size=(H, W))  # [B, C, 1, 1]
        A = self.A_conv(A)  # [B, filters, 1, 1]
        A = A.permute(0, 2, 3, 1).contiguous()  # [B, 1, 1, filters]
        A = torch.diag_embed(A.squeeze())  # [B, filters, filters]（对角矩阵）
        # 构造超边关联矩阵 M
        M = self.M_conv(x)  # [B, edges, H, W]
        M = M.permute(0, 2, 3, 1).contiguous()  # [B, H, W, edges]
        M = M.view(-1, self.vertices, self.edges)  # [B, V, E]
        
        H = torch.matmul(phi, torch.matmul(A, torch.matmul(phi.transpose(1, 2), M)))
        H = torch.abs(H)

        if self.theta1 != 0.0:
            mean_H = self.theta1*torch.mean(H,dim=[1,2],keepdim=True)
            H = torch.where(H < mean_H, 0.0, H)
        D = H.sum(dim=2)
        D_H = torch.mul(torch.unsqueeze(torch.pow(D + 1e-10, -0.5), dim=-1), H)
        B = H.sum(dim=1)
        B = torch.diag_embed(torch.pow(B + 1e-10, -1))
        x_ = torch.permute(x, (0, 2, 3, 1)).contiguous()
        features = x_.view(-1, self.vertices, self.in_features)

        out = features - torch.matmul(D_H, torch.matmul(B, torch.matmul(D_H.transpose(1, 2), features)))
        out = torch.matmul(out, self.weight_2)

        if self.apply_bias:
            out = out + self.bias_2
        out = torch.permute(out, (0, 2, 1)).contiguous()
        out = out.view(-1, self.out_features, self.features_height, self.features_width)

        return out


class HypergraphConvBeta(HypergraphConv):
    """
    基于基础超图卷积添加Beta分布概率连接矩阵的简化版本
    保持基础超图卷积的简洁性，只添加Beta分布功能
    """
    def __init__(
            self,
            in_features=1024,
            out_features=1024,
            features_height=18,
            features_width=9,
            edges=256,
            filters=128,
            apply_bias=True,
            theta1=0.0,
            use_beta_distribution=True,  # 是否使用Beta分布生成概率连接矩阵
            dataset_name='SYSU-MM01',   # 数据集名称，用于选择合适的分布参数
            beta_params=None            # 外部传入的Beta分布参数
    ):
        super().__init__(
            in_features=in_features,
            out_features=out_features,
            features_height=features_height,
            features_width=features_width,
            edges=edges,
            filters=filters,
            apply_bias=apply_bias,
            theta1=theta1
        )

        self.use_beta_distribution = use_beta_distribution
        self.dataset_name = dataset_name

        # 如果使用Beta分布，替换原来的M_conv
        if self.use_beta_distribution:
            # 移除原来的M_conv
            delattr(self, 'M_conv')

            # 初始化Beta分布生成器
            self.M_conv = BetaDistributionM(
                in_channels=in_features,
                edges=edges,
                vertices=features_height * features_width,
                dataset_name=dataset_name,
                beta_params=beta_params
            )

    def forward(self, x, modality_hint=None):
        """
        前向传播，支持模态提示
        Args:
            x: 输入特征 [B, C, H, W]
            modality_hint: 模态提示 ('visible_original', 'visible_enhanced', 'thermal_original', 'thermal_enhanced')
        """
        # Get input dimensions
        _, _, H, W = x.size()

        # 获取顶点表示 phi
        phi = self.phi_conv(x)  # [B, filters, H, W]
        phi = phi.permute(0, 2, 3, 1).contiguous()  # [B, H, W, filters]
        phi = phi.view(-1, self.vertices, self.filters)  # [B, V, filters]

        # 构造超边权重 A（注意：batch-wise）
        A = F.avg_pool2d(x, kernel_size=(H, W))  # [B, C, 1, 1]
        A = self.A_conv(A)  # [B, filters, 1, 1]
        A = A.permute(0, 2, 3, 1).contiguous()  # [B, 1, 1, filters]
        A = torch.diag_embed(A.squeeze())  # [B, filters, filters]（对角矩阵）

        # 构造超边关联矩阵 M
        if self.use_beta_distribution:
            # 使用Beta分布生成概率化连接矩阵
            M = self.M_conv(x, modality_hint)  # [B, V, E] 概率值
        else:
            # 原来的方式（保持兼容性）
            M = self.M_conv(x)  # [B, edges, H, W]
            M = M.permute(0, 2, 3, 1).contiguous()  # [B, H, W, edges]
            M = M.view(-1, self.vertices, self.edges)  # [B, V, E]

        # 计算超图关联矩阵（与基础版本完全相同）
        H = torch.matmul(phi, torch.matmul(A, torch.matmul(phi.transpose(1, 2), M)))
        H = torch.abs(H)

        # 应用稀疏化阈值（与基础版本完全相同）
        if self.theta1 != 0.0:
            mean_H = self.theta1 * torch.mean(H, dim=[1, 2], keepdim=True)
            H = torch.where(H < mean_H, 0.0, H)

        # 计算度矩阵和归一化（与基础版本完全相同）
        D = H.sum(dim=2)
        D_H = torch.mul(torch.unsqueeze(torch.pow(D + 1e-10, -0.5), dim=-1), H)
        B_matrix = H.sum(dim=1)
        B_matrix = torch.diag_embed(torch.pow(B_matrix + 1e-10, -1))

        # 准备特征矩阵（与基础版本完全相同）
        x_ = torch.permute(x, (0, 2, 3, 1)).contiguous()
        features = x_.view(-1, self.vertices, self.in_features)

        # 超图卷积操作（与基础版本完全相同）
        out = features - torch.matmul(D_H, torch.matmul(B_matrix, torch.matmul(D_H.transpose(1, 2), features)))
        out = torch.matmul(out, self.weight_2)

        # 添加偏置（与基础版本完全相同）
        if self.apply_bias:
            out = out + self.bias_2

        # 重塑输出（与基础版本完全相同）
        out = torch.permute(out, (0, 2, 1)).contiguous()
        out = out.view(-1, self.out_features, self.features_height, self.features_width)

        return out


class HypergraphConvDiagonal(HypergraphConv):
    """
    基于基础超图卷积添加对角索引和辅助节点处理的版本
    不包含Beta分布，专注于LSE^HVD对角局部性索引优化
    """
    def __init__(
            self,
            in_features=1024,
            out_features=1024,
            features_height=18,
            features_width=9,
            edges=256,
            filters=128,
            apply_bias=True,
            theta1=0.0,
            use_diagonal=True,        # 是否使用对角超图卷积
            max_depth=3,             # 索引树的最大深度
            diagonal_threshold=0.5,   # 对角相邻节点交集的阈值
            simplified_mode=False,    # 是否使用简化模式
            fusion_strategy='weighted_attention'  # 融合策略
    ):
        super().__init__(
            in_features=in_features,
            out_features=out_features,
            features_height=features_height,
            features_width=features_width,
            edges=edges,
            filters=filters,
            apply_bias=apply_bias,
            theta1=theta1
        )

        self.use_diagonal = use_diagonal
        self.max_depth = max_depth
        self.diagonal_threshold = diagonal_threshold
        self.simplified_mode = simplified_mode
        self.fusion_strategy = fusion_strategy

        # 初始化对角索引相关参数
        if self.use_diagonal and not self.simplified_mode:
            # 辅助节点融合权重
            self.aux_fusion_weight = nn.Parameter(
                torch.ones(max_depth) * 0.1, requires_grad=True
            )

            # 注意力机制相关参数（用于weighted_attention融合策略）
            if fusion_strategy == 'weighted_attention':
                self.embed_dim = min(128, edges // 2)
                self.aux_attention = nn.MultiheadAttention(
                    embed_dim=self.embed_dim,
                    num_heads=4,
                    batch_first=True
                )
                self.aux_transform = nn.Linear(edges, self.embed_dim)
                self.depth_embedding = nn.Embedding(max_depth + 1, self.embed_dim)
                self.aux_output_transform = nn.Linear(self.embed_dim, edges)

    def forward(self, x):
        """
        前向传播，应用对角索引优化
        Args:
            x: 输入特征 [B, C, H, W]
        """
        # Get input dimensions
        _, _, H, W = x.size()

        # 获取顶点表示 phi
        phi = self.phi_conv(x)  # [B, filters, H, W]
        phi = phi.permute(0, 2, 3, 1).contiguous()  # [B, H, W, filters]
        phi = phi.view(-1, self.vertices, self.filters)  # [B, V, filters]

        # 构造超边权重 A（注意：batch-wise）
        A = F.avg_pool2d(x, kernel_size=(H, W))  # [B, C, 1, 1]
        A = self.A_conv(A)  # [B, filters, 1, 1]
        A = A.permute(0, 2, 3, 1).contiguous()  # [B, 1, 1, filters]
        A = torch.diag_embed(A.squeeze())  # [B, filters, filters]（对角矩阵）

        # 构造超边关联矩阵 M（使用原始方式，不使用Beta分布）
        M = self.M_conv(x)  # [B, edges, H, W]
        M = M.permute(0, 2, 3, 1).contiguous()  # [B, H, W, edges]
        M = M.view(-1, self.vertices, self.edges)  # [B, V, E]

        # 计算超图关联矩阵
        H = torch.matmul(phi, torch.matmul(A, torch.matmul(phi.transpose(1, 2), M)))
        H = torch.abs(H)  # [B, V, E]

        # 应用LSE^HVD对角局部性索引优化
        if self.use_diagonal and not self.simplified_mode:
            H = self.build_diagonal_index(H)

        # 应用稀疏化阈值
        if self.theta1 != 0.0:
            mean_H = self.theta1 * torch.mean(H, dim=[1, 2], keepdim=True)
            H = torch.where(H < mean_H, 0.0, H)

        # 计算度矩阵和归一化（与基础版本完全相同）
        D = H.sum(dim=2)
        D_H = torch.mul(torch.unsqueeze(torch.pow(D + 1e-10, -0.5), dim=-1), H)
        B_matrix = H.sum(dim=1)
        B_matrix = torch.diag_embed(torch.pow(B_matrix + 1e-10, -1))

        # 准备特征矩阵
        x_reshaped = torch.permute(x, (0, 2, 3, 1)).contiguous()
        features = x_reshaped.view(-1, self.vertices, self.in_features)

        # 超图卷积操作
        out = features - torch.matmul(D_H, torch.matmul(B_matrix, torch.matmul(D_H.transpose(1, 2), features)))
        out = torch.matmul(out, self.weight_2)

        # 添加偏置
        if self.apply_bias:
            out = out + self.bias_2

        # 重塑输出
        out = torch.permute(out, (0, 2, 1)).contiguous()
        out = out.view(-1, self.out_features, self.features_height, self.features_width)

        return out

    def build_diagonal_index(self, H):
        """
        构建基于LSE^HVD对角局部性的索引树
        Args:
            H: 超图关联矩阵 [B, V, E]
        Returns:
            优化后的超图关联矩阵
        """
        if not self.use_diagonal or self.simplified_mode:
            return H

        B, V, E = H.size()

        # 初始化索引树结构
        indexing_tree = {}
        auxiliary_nodes = {}

        # 为每个batch构建索引树
        for b in range(B):
            batch_tree = {}
            batch_aux = {}

            # 构建叶节点 - 从(1,1)开始
            for g in range(1, self.max_depth + 1):
                for k in range(1, g + 1):
                    node_key = (k, g)

                    # 计算节点对应的数据区域，确保不越界
                    v_start = max(0, (k - 1) * V // g)
                    v_end = min(V, k * V // g)
                    e_start = max(0, (g - 1) * E // self.max_depth)
                    e_end = min(E, g * E // self.max_depth)

                    # 确保区域有效
                    if v_start >= v_end or e_start >= e_end:
                        continue

                    # 提取对应区域的数据
                    node_data = H[b, v_start:v_end, e_start:e_end].clone()

                    # 创建叶节点
                    batch_tree[node_key] = {
                        'data': node_data,
                        'next': (k, g + 1) if g < self.max_depth else None,
                        'jump': (k + 1, g) if k < g else None,
                        'is_leaf': True,
                        'depth': 0
                    }

            # 构建辅助节点 - 存储对角相邻节点的公共部分
            for depth in range(1, self.max_depth):
                for g in range(1, self.max_depth):
                    for k in range(1, g):
                        # 检查对角相邻的节点
                        curr_key = (k, g)
                        next_key = (k, g + 1)
                        jump_key = (k + 1, g)
                        aux_key = (k + 1, g + 1)

                        if (curr_key in batch_tree and next_key in batch_tree and
                            jump_key in batch_tree):

                            try:
                                # 计算公共节点
                                next_data = batch_tree[next_key]['data']
                                jump_data = batch_tree[jump_key]['data']

                                # 检查数据有效性
                                if next_data.numel() == 0 or jump_data.numel() == 0:
                                    continue

                                # 使用更复杂的交集计算
                                intersection = self._compute_diagonal_intersection(
                                    next_data, jump_data, depth
                                )

                                if torch.sum(torch.abs(intersection)) > 1e-6:
                                    # 创建辅助节点
                                    batch_aux[aux_key] = {
                                        'data': intersection,
                                        'depth': depth,
                                        'is_leaf': False,
                                        'parent_nodes': [next_key, jump_key]
                                    }

                                    # 从原节点中移除公共部分
                                    batch_tree[next_key]['data'] = self._remove_intersection(
                                        batch_tree[next_key]['data'], intersection
                                    )
                                    batch_tree[jump_key]['data'] = self._remove_intersection(
                                        batch_tree[jump_key]['data'], intersection
                                    )
                            except Exception:
                                # 如果出现维度不匹配等错误，跳过这个辅助节点的创建
                                continue

            indexing_tree[b] = batch_tree
            auxiliary_nodes[b] = batch_aux

        # 融合辅助节点信息回原始超图
        H_optimized = self._fuse_auxiliary_nodes(H, indexing_tree, auxiliary_nodes)

        return H_optimized

    def _compute_diagonal_intersection(self, matrix1, matrix2, depth):
        """计算对角相邻节点的交集，考虑深度信息"""
        try:
            # 检查输入矩阵是否有效
            if matrix1 is None or matrix2 is None:
                return torch.zeros_like(matrix1 if matrix1 is not None else matrix2)

            if matrix1.numel() == 0 or matrix2.numel() == 0:
                return torch.zeros((1, 1), device=matrix1.device)

            # 确保两个矩阵维度匹配
            if matrix1.shape != matrix2.shape:
                # 创建新的矩阵用于交集计算
                min_h = min(matrix1.size(0), matrix2.size(0))
                min_w = min(matrix1.size(1), matrix2.size(1))

                # 确保尺寸大于0
                if min_h <= 0 or min_w <= 0:
                    return torch.zeros((1, 1), device=matrix1.device)

                # 裁剪矩阵到相同尺寸
                matrix1_resized = matrix1[:min_h, :min_w].clone()
                matrix2_resized = matrix2[:min_h, :min_w].clone()

                # 使用加权最小值来计算交集，深度越深权重越小
                depth_weight = 1.0 / (depth + 1)
                intersection = torch.min(matrix1_resized, matrix2_resized) * depth_weight
            else:
                # 矩阵尺寸已经匹配
                depth_weight = 1.0 / (depth + 1)
                intersection = torch.min(matrix1, matrix2) * depth_weight

            # 应用阈值过滤
            threshold = torch.mean(intersection) * self.diagonal_threshold
            intersection = torch.where(intersection > threshold, intersection, torch.zeros_like(intersection))

            return intersection

        except Exception as e:
            print(f"Error in _compute_diagonal_intersection: {e}")
            # 返回安全的默认值
            return torch.zeros((1, 1), device=matrix1.device if matrix1 is not None else
                               (matrix2.device if matrix2 is not None else 'cpu'))

    def _remove_intersection(self, original, intersection):
        """从原始矩阵中移除交集部分"""
        # 确保维度匹配
        if original.shape != intersection.shape:
            # 如果交集更小，只在对应区域进行移除
            min_h = min(original.size(0), intersection.size(0))
            min_w = min(original.size(1), intersection.size(1))

            # 创建与原始矩阵相同大小的移除掩码
            removal_mask = torch.zeros_like(original)
            removal_mask[:min_h, :min_w] = (intersection[:min_h, :min_w] > 1e-6).float()
        else:
            removal_mask = (intersection > 1e-6).float()

        # 使用软移除，避免完全置零
        removal_factor = 0.5  # 保留50%的原始值（比原来的0.7更保守）
        return original * (1 - removal_mask * removal_factor)

    def _fuse_auxiliary_nodes(self, H, indexing_tree, auxiliary_nodes):
        """融合辅助节点信息回原始超图"""
        H_fused = H.clone()

        if self.fusion_strategy == 'simple_average':
            return self._simple_fusion(H_fused, auxiliary_nodes)
        elif self.fusion_strategy == 'weighted_attention':
            return self._attention_fusion(H_fused, auxiliary_nodes)
        elif self.fusion_strategy == 'advanced':
            return self._advanced_fusion(H_fused, indexing_tree, auxiliary_nodes)
        else:
            return H_fused

    def _simple_fusion(self, H, auxiliary_nodes):
        """简单平均融合策略"""
        for b in range(H.size(0)):
            if b in auxiliary_nodes:
                for aux_key, aux_info in auxiliary_nodes[b].items():
                    k, g = aux_key
                    depth = aux_info['depth']
                    aux_data = aux_info['data']

                    # 检查辅助数据有效性
                    if aux_data.numel() == 0:
                        continue

                    # 简单加权平均
                    weight = self.aux_fusion_weight[depth - 1] if depth <= len(self.aux_fusion_weight) else 0.1

                    # 将辅助节点数据融合回对应区域
                    v_start = max(0, (k - 1) * H.size(1) // g)
                    v_end = min(H.size(1), k * H.size(1) // g)
                    e_start = max(0, (g - 1) * H.size(2) // self.max_depth)
                    e_end = min(H.size(2), g * H.size(2) // self.max_depth)

                    # 确保区域有效
                    if v_start >= v_end or e_start >= e_end:
                        continue

                    try:
                        target_region = H[b, v_start:v_end, e_start:e_end]

                        # 处理维度不匹配的情况
                        if target_region.shape == aux_data.shape:
                            H[b, v_start:v_end, e_start:e_end] += weight * aux_data
                        else:
                            # 调整辅助数据尺寸以匹配目标区域
                            min_h = min(target_region.size(0), aux_data.size(0))
                            min_w = min(target_region.size(1), aux_data.size(1))

                            if min_h > 0 and min_w > 0:
                                H[b, v_start:v_start+min_h, e_start:e_start+min_w] += weight * aux_data[:min_h, :min_w]
                    except Exception:
                        # 如果融合失败，跳过这个辅助节点
                        continue

        return H

    def _attention_fusion(self, H, auxiliary_nodes):
        """基于注意力机制的融合策略"""
        for b in range(H.size(0)):
            if b in auxiliary_nodes and len(auxiliary_nodes[b]) > 0:
                # 收集所有辅助节点数据
                aux_data_list = []
                aux_positions = []

                for aux_key, aux_info in auxiliary_nodes[b].items():
                    k, g = aux_key
                    depth = aux_info['depth']
                    aux_data = aux_info['data']

                    # 展平辅助数据并变换到统一维度
                    aux_flat = aux_data.flatten().unsqueeze(0)  # [1, features]
                    if aux_flat.size(1) != self.embed_dim:
                        # 调整到目标维度
                        if aux_flat.size(1) > self.embed_dim:
                            aux_flat = aux_flat[:, :self.embed_dim]
                        else:
                            # 填充到目标维度
                            padding = torch.zeros(1, self.embed_dim - aux_flat.size(1), device=aux_flat.device)
                            aux_flat = torch.cat([aux_flat, padding], dim=1)

                    aux_data_list.append(aux_flat)
                    aux_positions.append((k, g, depth))

                if aux_data_list:
                    # 堆叠所有辅助节点数据
                    aux_stack = torch.cat(aux_data_list, dim=0)  # [num_aux, embed_dim]

                    # 添加深度嵌入
                    depth_embs = []
                    for _, _, depth in aux_positions:
                        depth_idx = min(depth, self.max_depth) - 1
                        depth_emb = self.depth_embedding(torch.tensor(depth_idx, device=H.device))
                        depth_embs.append(depth_emb.unsqueeze(0))

                    if len(depth_embs) > 0:
                        depth_stack = torch.cat(depth_embs, dim=0)  # [num_aux, embed_dim]

                        # 应用注意力机制
                        aux_enhanced, _ = self.aux_attention(
                            aux_stack.unsqueeze(0),  # [1, num_aux, embed_dim]
                            depth_stack.unsqueeze(0),  # [1, num_aux, embed_dim]
                            depth_stack.unsqueeze(0)   # [1, num_aux, embed_dim]
                        )

                        # 将增强后的辅助节点数据融合回原图
                        for i, (k, g, depth) in enumerate(aux_positions):
                            # 变换回原始维度
                            enhanced_flat = self.aux_output_transform(aux_enhanced[0, i])  # [edges]

                            # 重塑为原始形状
                            original_shape = auxiliary_nodes[b][(k, g)]['data'].shape
                            if enhanced_flat.numel() >= original_shape.numel():
                                enhanced_data = enhanced_flat[:original_shape.numel()].view(original_shape)
                            else:
                                # 如果维度不足，用零填充
                                padding_size = original_shape.numel() - enhanced_flat.numel()
                                padding = torch.zeros(padding_size, device=enhanced_flat.device)
                                enhanced_flat_padded = torch.cat([enhanced_flat, padding])
                                enhanced_data = enhanced_flat_padded.view(original_shape)

                            weight = self.aux_fusion_weight[depth - 1] if depth <= len(self.aux_fusion_weight) else 0.1

                            # 融合到对应区域
                            v_start = (k - 1) * H.size(1) // g
                            v_end = k * H.size(1) // g
                            e_start = (g - 1) * H.size(2) // self.max_depth
                            e_end = g * H.size(2) // self.max_depth

                            if (v_end <= H.size(1) and e_end <= H.size(2) and
                                H[b, v_start:v_end, e_start:e_end].shape == enhanced_data.shape):
                                H[b, v_start:v_end, e_start:e_end] += weight * enhanced_data

        return H

    def _advanced_fusion(self, H, indexing_tree, auxiliary_nodes):
        """高级融合策略：结合树结构和层次信息"""
        for b in range(H.size(0)):
            if b in auxiliary_nodes:
                # 按深度排序辅助节点
                sorted_aux = sorted(auxiliary_nodes[b].items(),
                                  key=lambda x: x[1]['depth'])

                for aux_key, aux_info in sorted_aux:
                    k, g = aux_key
                    depth = aux_info['depth']
                    aux_data = aux_info['data']
                    parent_nodes = aux_info.get('parent_nodes', [])

                    # 检查辅助数据有效性
                    if aux_data.numel() == 0:
                        continue

                    try:
                        # 计算动态权重
                        dynamic_weight = self._compute_dynamic_weight(
                            aux_data, depth, parent_nodes, indexing_tree[b]
                        )

                        # 应用非线性变换
                        transformed_data = torch.tanh(aux_data) * dynamic_weight

                        # 融合到对应区域
                        v_start = max(0, (k - 1) * H.size(1) // g)
                        v_end = min(H.size(1), k * H.size(1) // g)
                        e_start = max(0, (g - 1) * H.size(2) // self.max_depth)
                        e_end = min(H.size(2), g * H.size(2) // self.max_depth)

                        # 确保区域有效
                        if v_start >= v_end or e_start >= e_end:
                            continue

                        target_region = H[b, v_start:v_end, e_start:e_end]

                        # 处理维度不匹配的情况
                        if target_region.shape == transformed_data.shape:
                            H[b, v_start:v_end, e_start:e_end] += transformed_data
                        else:
                            # 调整变换数据尺寸以匹配目标区域
                            min_h = min(target_region.size(0), transformed_data.size(0))
                            min_w = min(target_region.size(1), transformed_data.size(1))

                            if min_h > 0 and min_w > 0:
                                H[b, v_start:v_start+min_h, e_start:e_start+min_w] += transformed_data[:min_h, :min_w]

                    except Exception:
                        # 如果融合失败，跳过这个辅助节点
                        continue

        return H

    def _compute_dynamic_weight(self, aux_data, depth, parent_nodes, tree):
        """计算动态权重"""
        base_weight = self.aux_fusion_weight[depth - 1] if depth <= len(self.aux_fusion_weight) else 0.1

        # 基于数据强度调整权重
        data_intensity = torch.mean(torch.abs(aux_data))
        intensity_factor = torch.sigmoid(data_intensity - 0.5)

        # 基于父节点相似性调整权重
        similarity_factor = 1.0
        if len(parent_nodes) >= 2:
            parent_data = [tree[pn]['data'] for pn in parent_nodes if pn in tree]
            if len(parent_data) >= 2:
                try:
                    # 展平并确保维度匹配
                    data1_flat = parent_data[0].flatten()
                    data2_flat = parent_data[1].flatten()

                    # 取较小的维度以避免维度不匹配
                    min_size = min(data1_flat.size(0), data2_flat.size(0))
                    if min_size > 0:
                        data1_truncated = data1_flat[:min_size].unsqueeze(0)
                        data2_truncated = data2_flat[:min_size].unsqueeze(0)

                        sim = F.cosine_similarity(data1_truncated, data2_truncated)
                        similarity_factor = torch.abs(sim).item()
                except Exception:
                    # 如果计算相似性失败，使用默认值
                    similarity_factor = 1.0

        return base_weight * intensity_factor * similarity_factor


class HypergraphConvDiag(HypergraphConv):
    def __init__(
            self,
            in_features=1024,
            out_features=1024,
            features_height=18,
            features_width=9,
            edges=256,
            filters=128,
            apply_bias=True,
            theta1=0.0,#超图卷积的阈值参数
            use_diagonal=True, #是否使用对角超图卷积
            max_depth=3, #索引树的最大深度
            diagonal_threshold=0.5, #对角相邻节点交集的阈值
            simplified_mode=False,
            fusion_strategy='weighted_attention',
            use_beta_distribution=True, #是否使用Beta分布生成概率连接矩阵
            dataset_name='SYSU-MM01', #数据集名称，用于选择合适的分布参数
            beta_params=None  # 外部传入的Beta分布参数
    ):
        super().__init__(
            in_features=in_features,
            out_features=out_features,
            features_height=features_height,
            features_width=features_width,
            edges=edges,
            filters=filters,
            apply_bias=apply_bias,
            theta1=theta1
        )
        self.use_diagonal = use_diagonal
        self.max_depth = max_depth
        self.diagonal_threshold = diagonal_threshold
        self.simplified_mode = simplified_mode
        self.fusion_strategy = fusion_strategy
        self.use_beta_distribution = use_beta_distribution
        self.dataset_name = dataset_name

        # 初始化Beta分布生成器（替代原来的M_conv）
        if self.use_beta_distribution:
            self.M_conv = BetaDistributionM(
                in_channels=in_features,
                edges=edges,
                vertices=features_height * features_width,
                dataset_name=dataset_name,
                beta_params=beta_params  # 传递Beta分布参数
            )

        # 为辅助节点融合添加可学习参数
        if not simplified_mode and use_diagonal:
            self.aux_fusion_weight = nn.Parameter(torch.ones(max_depth))
            # 使用更小的嵌入维度避免维度不匹配
            self.embed_dim = min(64, edges)
            self.aux_attention = nn.MultiheadAttention(
                embed_dim=self.embed_dim,
                num_heads=min(4, self.embed_dim // 16),
                batch_first=True
            )
            self.aux_transform = nn.Linear(edges, self.embed_dim)
            self.depth_embedding = nn.Embedding(max_depth + 1, self.embed_dim)
            self.aux_output_transform = nn.Linear(self.embed_dim, edges)

    def build_diagonal_index(self, H):
        """
        构建基于LSE^HVD对角局部性的索引树
        Args:
            H: 超图关联矩阵 [B, V, E]
        Returns:
            优化后的超图关联矩阵和辅助节点信息
        """
        if not self.use_diagonal or self.simplified_mode:
            return H

        B, V, E = H.size()
        _ = H.device  # Keep for potential future use

        # 初始化索引树结构
        indexing_tree = {}
        auxiliary_nodes = {}

        # 为每个batch构建索引树
        for b in range(B):
            batch_tree = {}
            batch_aux = {}

            # 构建叶节点 - 从(1,1)开始
            for g in range(1, self.max_depth + 1):
                for k in range(1, g + 1):
                    node_key = (k, g)

                    # 计算节点对应的数据区域，确保不越界
                    v_start = max(0, (k - 1) * V // g)
                    v_end = min(V, k * V // g)
                    e_start = max(0, (g - 1) * E // self.max_depth)
                    e_end = min(E, g * E // self.max_depth)

                    # 确保区域有效
                    if v_start >= v_end or e_start >= e_end:
                        continue

                    # 提取对应区域的数据
                    node_data = H[b, v_start:v_end, e_start:e_end].clone()

                    # 创建叶节点
                    batch_tree[node_key] = {
                        'data': node_data,
                        'next': (k, g + 1) if g < self.max_depth else None,
                        'jump': (k + 1, g) if k < g else None,
                        'is_leaf': True,
                        'depth': 0
                    }

            # 构建辅助节点 - 存储对角相邻节点的公共部分
            for depth in range(1, self.max_depth):
                for g in range(1, self.max_depth):
                    for k in range(1, g):
                        # 检查对角相邻的节点
                        curr_key = (k, g)
                        next_key = (k, g + 1)
                        jump_key = (k + 1, g)
                        aux_key = (k + 1, g + 1)

                        if (curr_key in batch_tree and next_key in batch_tree and
                            jump_key in batch_tree):

                            try:
                                # 计算公共节点
                                next_data = batch_tree[next_key]['data']
                                jump_data = batch_tree[jump_key]['data']

                                # 检查数据有效性
                                if next_data.numel() == 0 or jump_data.numel() == 0:
                                    continue

                                # 使用更复杂的交集计算
                                intersection = self._compute_diagonal_intersection(
                                    next_data, jump_data, depth
                                )

                                if torch.sum(torch.abs(intersection)) > 1e-6:
                                    # 创建辅助节点
                                    batch_aux[aux_key] = {
                                        'data': intersection,
                                        'depth': depth,
                                        'is_leaf': False,
                                        'parent_nodes': [next_key, jump_key]
                                    }

                                    # 从原节点中移除公共部分
                                    batch_tree[next_key]['data'] = self._remove_intersection(
                                        batch_tree[next_key]['data'], intersection
                                    )
                                    batch_tree[jump_key]['data'] = self._remove_intersection(
                                        batch_tree[jump_key]['data'], intersection
                                    )
                            except Exception:
                                # 如果出现维度不匹配等错误，跳过这个辅助节点的创建
                                continue

            indexing_tree[b] = batch_tree
            auxiliary_nodes[b] = batch_aux

        # 融合辅助节点信息回原始超图
        H_optimized = self._fuse_auxiliary_nodes(H, indexing_tree, auxiliary_nodes)

        return H_optimized

    def _compute_diagonal_intersection(self, matrix1, matrix2, depth):
        """计算对角相邻节点的交集，考虑深度信息"""
        try:
            # 检查输入矩阵是否有效
            if matrix1 is None or matrix2 is None:
                return torch.zeros_like(matrix1 if matrix1 is not None else matrix2)
            
            if matrix1.numel() == 0 or matrix2.numel() == 0:
                return torch.zeros((1, 1), device=matrix1.device)
            
            # 确保两个矩阵维度匹配
            if matrix1.shape != matrix2.shape:
                # 创建新的矩阵用于交集计算
                min_h = min(matrix1.size(0), matrix2.size(0))
                min_w = min(matrix1.size(1), matrix2.size(1))
                
                # 确保尺寸大于0
                if min_h <= 0 or min_w <= 0:
                    return torch.zeros((1, 1), device=matrix1.device)
                
                # 裁剪矩阵到相同尺寸
                matrix1_resized = matrix1[:min_h, :min_w].clone()
                matrix2_resized = matrix2[:min_h, :min_w].clone()
                
                # 使用加权最小值来计算交集，深度越深权重越小
                depth_weight = 1.0 / (depth + 1)
                intersection = torch.min(matrix1_resized, matrix2_resized) * depth_weight
            else:
                # 矩阵尺寸已经匹配
                depth_weight = 1.0 / (depth + 1)
                intersection = torch.min(matrix1, matrix2) * depth_weight
            
            # 应用阈值过滤
            threshold = torch.mean(intersection) * self.diagonal_threshold
            intersection = torch.where(intersection > threshold, intersection, torch.zeros_like(intersection))
            
            return intersection
            
        except Exception as e:
            print(f"Error in _compute_diagonal_intersection: {e}")
            print(f"Matrix1 shape: {matrix1.shape if matrix1 is not None else 'None'}")
            print(f"Matrix2 shape: {matrix2.shape if matrix2 is not None else 'None'}")
            # 返回安全的默认值
            return torch.zeros((1, 1), device=matrix1.device if matrix1 is not None else 
                               (matrix2.device if matrix2 is not None else 'cpu'))

    def _remove_intersection(self, original, intersection):
        """从原始矩阵中移除交集部分"""
        # 确保维度匹配
        if original.shape != intersection.shape:
            # 如果交集更小，只在对应区域进行移除
            min_h = min(original.size(0), intersection.size(0))
            min_w = min(original.size(1), intersection.size(1))

            # 创建与原始矩阵相同大小的移除掩码
            removal_mask = torch.zeros_like(original)
            removal_mask[:min_h, :min_w] = (intersection[:min_h, :min_w] > 1e-6).float()
        else:
            removal_mask = (intersection > 1e-6).float()

        # 使用软移除，避免完全置零
        removal_factor = 0.7  # 保留30%的原始值
        return original * (1 - removal_mask * removal_factor)

    def _fuse_auxiliary_nodes(self, H, indexing_tree, auxiliary_nodes):
        """融合辅助节点信息回原始超图"""
        _, _, _ = H.size()  # B, V, E - keep for potential future use
        H_fused = H.clone()

        if self.fusion_strategy == 'simple_average':
            return self._simple_fusion(H_fused, auxiliary_nodes)
        elif self.fusion_strategy == 'weighted_attention':
            return self._attention_fusion(H_fused, auxiliary_nodes)
        elif self.fusion_strategy == 'advanced':
            return self._advanced_fusion(H_fused, indexing_tree, auxiliary_nodes)
        else:
            return H_fused

    def _simple_fusion(self, H, auxiliary_nodes):
        """简单平均融合策略"""
        for b in range(H.size(0)):
            if b in auxiliary_nodes:
                for aux_key, aux_info in auxiliary_nodes[b].items():
                    k, g = aux_key
                    depth = aux_info['depth']
                    aux_data = aux_info['data']

                    # 检查辅助数据有效性
                    if aux_data.numel() == 0:
                        continue

                    # 简单加权平均
                    weight = self.aux_fusion_weight[depth - 1] if depth <= len(self.aux_fusion_weight) else 0.1

                    # 将辅助节点数据融合回对应区域
                    v_start = max(0, (k - 1) * H.size(1) // g)
                    v_end = min(H.size(1), k * H.size(1) // g)
                    e_start = max(0, (g - 1) * H.size(2) // self.max_depth)
                    e_end = min(H.size(2), g * H.size(2) // self.max_depth)

                    # 确保区域有效
                    if v_start >= v_end or e_start >= e_end:
                        continue

                    try:
                        target_region = H[b, v_start:v_end, e_start:e_end]

                        # 处理维度不匹配的情况
                        if target_region.shape == aux_data.shape:
                            H[b, v_start:v_end, e_start:e_end] += weight * aux_data
                        else:
                            # 调整辅助数据尺寸以匹配目标区域
                            min_h = min(target_region.size(0), aux_data.size(0))
                            min_w = min(target_region.size(1), aux_data.size(1))

                            if min_h > 0 and min_w > 0:
                                H[b, v_start:v_start+min_h, e_start:e_start+min_w] += weight * aux_data[:min_h, :min_w]
                    except Exception:
                        # 如果融合失败，跳过这个辅助节点
                        continue

        return H

    def _attention_fusion(self, H, auxiliary_nodes):
        """基于注意力机制的融合策略"""
        for b in range(H.size(0)):
            if b in auxiliary_nodes and len(auxiliary_nodes[b]) > 0:
                # 收集所有辅助节点数据
                aux_data_list = []
                aux_positions = []

                for aux_key, aux_info in auxiliary_nodes[b].items():
                    k, g = aux_key
                    depth = aux_info['depth']
                    aux_data = aux_info['data']

                    # 展平辅助数据并变换到统一维度
                    aux_flat = aux_data.flatten().unsqueeze(0)  # [1, features]
                    if aux_flat.size(1) != self.embed_dim:
                        # 调整到目标维度
                        if aux_flat.size(1) > self.embed_dim:
                            aux_flat = aux_flat[:, :self.embed_dim]
                        else:
                            # 填充到目标维度
                            padding = torch.zeros(1, self.embed_dim - aux_flat.size(1), device=aux_flat.device)
                            aux_flat = torch.cat([aux_flat, padding], dim=1)

                    aux_data_list.append(aux_flat)
                    aux_positions.append((k, g, depth))

                if aux_data_list:
                    # 堆叠所有辅助节点数据
                    aux_stack = torch.cat(aux_data_list, dim=0)  # [num_aux, embed_dim]

                    # 添加深度嵌入
                    depth_embs = []
                    for _, _, depth in aux_positions:
                        depth_idx = min(depth, self.max_depth) - 1
                        depth_emb = self.depth_embedding(torch.tensor(depth_idx, device=H.device))
                        depth_embs.append(depth_emb.unsqueeze(0))

                    if len(depth_embs) > 0:
                        depth_stack = torch.cat(depth_embs, dim=0)  # [num_aux, embed_dim]

                        # 应用注意力机制
                        aux_enhanced, _ = self.aux_attention(
                            aux_stack.unsqueeze(0),  # [1, num_aux, embed_dim]
                            depth_stack.unsqueeze(0),  # [1, num_aux, embed_dim]
                            depth_stack.unsqueeze(0)   # [1, num_aux, embed_dim]
                        )

                        # 将增强后的辅助节点数据融合回原图
                        for i, (k, g, depth) in enumerate(aux_positions):
                            # 变换回原始维度
                            enhanced_flat = self.aux_output_transform(aux_enhanced[0, i])  # [edges]

                            # 重塑为原始形状
                            original_shape = auxiliary_nodes[b][(k, g)]['data'].shape
                            if enhanced_flat.numel() >= original_shape.numel():
                                enhanced_data = enhanced_flat[:original_shape.numel()].view(original_shape)
                            else:
                                # 如果维度不足，用零填充
                                padding_size = original_shape.numel() - enhanced_flat.numel()
                                padding = torch.zeros(padding_size, device=enhanced_flat.device)
                                enhanced_flat_padded = torch.cat([enhanced_flat, padding])
                                enhanced_data = enhanced_flat_padded.view(original_shape)

                            weight = self.aux_fusion_weight[depth - 1] if depth <= len(self.aux_fusion_weight) else 0.1

                            # 融合到对应区域
                            v_start = (k - 1) * H.size(1) // g
                            v_end = k * H.size(1) // g
                            e_start = (g - 1) * H.size(2) // self.max_depth
                            e_end = g * H.size(2) // self.max_depth

                            if (v_end <= H.size(1) and e_end <= H.size(2) and
                                H[b, v_start:v_end, e_start:e_end].shape == enhanced_data.shape):
                                H[b, v_start:v_end, e_start:e_end] += weight * enhanced_data

        return H

    def _advanced_fusion(self, H, indexing_tree, auxiliary_nodes):
        """高级融合策略：结合树结构和层次信息"""
        for b in range(H.size(0)):
            if b in auxiliary_nodes:
                # 按深度排序辅助节点
                sorted_aux = sorted(auxiliary_nodes[b].items(),
                                  key=lambda x: x[1]['depth'])

                for aux_key, aux_info in sorted_aux:
                    k, g = aux_key
                    depth = aux_info['depth']
                    aux_data = aux_info['data']
                    parent_nodes = aux_info.get('parent_nodes', [])

                    # 检查辅助数据有效性
                    if aux_data.numel() == 0:
                        continue

                    try:
                        # 计算动态权重
                        dynamic_weight = self._compute_dynamic_weight(
                            aux_data, depth, parent_nodes, indexing_tree[b]
                        )

                        # 应用非线性变换
                        transformed_data = torch.tanh(aux_data) * dynamic_weight

                        # 融合到对应区域
                        v_start = max(0, (k - 1) * H.size(1) // g)
                        v_end = min(H.size(1), k * H.size(1) // g)
                        e_start = max(0, (g - 1) * H.size(2) // self.max_depth)
                        e_end = min(H.size(2), g * H.size(2) // self.max_depth)

                        # 确保区域有效
                        if v_start >= v_end or e_start >= e_end:
                            continue

                        target_region = H[b, v_start:v_end, e_start:e_end]

                        # 处理维度不匹配的情况
                        if target_region.shape == transformed_data.shape:
                            H[b, v_start:v_end, e_start:e_end] += transformed_data
                        else:
                            # 调整变换数据尺寸以匹配目标区域
                            min_h = min(target_region.size(0), transformed_data.size(0))
                            min_w = min(target_region.size(1), transformed_data.size(1))

                            if min_h > 0 and min_w > 0:
                                H[b, v_start:v_start+min_h, e_start:e_start+min_w] += transformed_data[:min_h, :min_w]

                    except Exception:
                        # 如果融合失败，跳过这个辅助节点
                        continue

        return H

    def _compute_dynamic_weight(self, aux_data, depth, parent_nodes, tree):
        """计算动态权重"""
        base_weight = self.aux_fusion_weight[depth - 1] if depth <= len(self.aux_fusion_weight) else 0.1

        # 基于数据强度调整权重
        data_intensity = torch.mean(torch.abs(aux_data))
        intensity_factor = torch.sigmoid(data_intensity - 0.5)

        # 基于父节点相似性调整权重
        similarity_factor = 1.0
        if len(parent_nodes) >= 2:
            parent_data = [tree[pn]['data'] for pn in parent_nodes if pn in tree]
            if len(parent_data) >= 2:
                try:
                    # 展平并确保维度匹配
                    data1_flat = parent_data[0].flatten()
                    data2_flat = parent_data[1].flatten()

                    # 取较小的维度以避免维度不匹配
                    min_size = min(data1_flat.size(0), data2_flat.size(0))
                    if min_size > 0:
                        data1_truncated = data1_flat[:min_size].unsqueeze(0)
                        data2_truncated = data2_flat[:min_size].unsqueeze(0)

                        sim = F.cosine_similarity(data1_truncated, data2_truncated)
                        similarity_factor = torch.abs(sim).item()
                except Exception:
                    # 如果计算相似性失败，使用默认值
                    similarity_factor = 1.0

        return base_weight * intensity_factor * similarity_factor

    def forward(self, x, modality_hint=None):
        # 获取输入维度
        _, _, height, width = x.size()

        # 获取顶点表示 phi,每个顶点有一个 filters 维的特征向量。
        phi = self.phi_conv(x)  # [B, filters, H, W]
        phi = phi.permute(0, 2, 3, 1).contiguous()  # [B, H, W, filters]
        phi = phi.view(-1, self.vertices, self.filters)  # [B, V, filters]

        # 构造超边权重 A（注意：batch-wise）
        A = F.avg_pool2d(x, kernel_size=(height, width))  # [B, C, 1, 1]
        A = self.A_conv(A)  # [B, filters, 1, 1]
        A = A.permute(0, 2, 3, 1).contiguous()  # [B, 1, 1, filters]
        A = torch.diag_embed(A.squeeze())  # [B, filters, filters]（对角矩阵）

        # 构造超边连接矩阵 M
        if self.use_beta_distribution:
            # 使用Beta分布生成概率化连接矩阵
            M = self.M_conv(x, modality_hint)  # [B, V, E] 概率值
        else:
            # 原来的方式
            '''若想做“稀疏化”处理（减少参与卷积的边），可以：
            对 M 加门控（如 threshold）
            或使用 ReLU、sigmoid、softmax 后处理'''
            M = self.M_conv(x)  # [B, edges, H, W]
            M = M.permute(0, 2, 3, 1).contiguous()  # [B, H, W, edges]
            M = M.view(-1, self.vertices, self.edges)  # [B, V, E]

        # 计算超图关联矩阵
        H = torch.matmul(phi, torch.matmul(A, torch.matmul(phi.transpose(1, 2), M)))
        H = torch.abs(H) #[B, V, E]

        # 应用LSE^HVD对角局部性索引优化
        if self.use_diagonal and not self.simplified_mode:
            H = self.build_diagonal_index(H)

        # 应用稀疏化阈值
        if self.theta1 != 0.0:
            mean_H = self.theta1 * torch.mean(H, dim=[1, 2], keepdim=True)
            H = torch.where(H < mean_H, 0.0, H)

        # 计算度矩阵和归一化
        D = H.sum(dim=2)
        D_H = torch.mul(torch.unsqueeze(torch.pow(D + 1e-10, -0.5), dim=-1), H)
        B_matrix = H.sum(dim=1)
        B_matrix = torch.diag_embed(torch.pow(B_matrix + 1e-10, -1))

        # 准备特征矩阵
        x_reshaped = torch.permute(x, (0, 2, 3, 1)).contiguous()
        features = x_reshaped.view(-1, self.vertices, self.in_features)

        # 超图卷积操作
        out = features - torch.matmul(D_H, torch.matmul(B_matrix, torch.matmul(D_H.transpose(1, 2), features)))
        out = torch.matmul(out, self.weight_2)

        # 添加偏置
        if self.apply_bias:
            out = out + self.bias_2

        # 重塑输出
        out = torch.permute(out, (0, 2, 1)).contiguous()
        out = out.view(-1, self.out_features, self.features_height, self.features_width)

        return out



