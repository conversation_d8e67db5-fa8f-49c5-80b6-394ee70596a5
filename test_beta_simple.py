#!/usr/bin/env python3
"""
简化的Beta分布测试
"""

import torch
from hypergraphs import HypergraphConvDiag

def test_beta_basic():
    """基本功能测试"""
    print("测试Beta分布基本功能...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"设备: {device}")
    
    # 创建测试数据
    x = torch.randn(2, 256, 24, 12).to(device)
    
    # 测试Beta分布模式
    model = HypergraphConvDiag(
        in_features=256,
        out_features=256,
        features_height=24,
        features_width=12,
        use_beta_distribution=True,
        dataset_name='SYSU-MM01'
    ).to(device)
    
    try:
        with torch.no_grad():
            output = model(x, 'visible_original')
        print(f"✓ Beta分布测试成功: {output.shape}")
        return True
    except Exception as e:
        print(f"✗ Beta分布测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_beta_basic()
    if success:
        print("✓ 基本测试通过")
    else:
        print("✗ 基本测试失败")
