#!/usr/bin/env python3
"""
测试新的HypergraphConvDiagonal类
"""

import torch
import torch.nn.functional as F
from hypergraphs import HypergraphConv, HypergraphConvBeta, HypergraphConvDiagonal, HypergraphConvDiag

def test_hypergraph_diagonal_basic():
    """测试HypergraphConvDiagonal基本功能"""
    print("=" * 60)
    print("测试HypergraphConvDiagonal基本功能")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 测试参数
    batch_size = 4
    in_features = 256
    height = 24
    width = 12
    edges = 256
    filters = 128
    
    # 创建测试数据
    torch.manual_seed(42)
    test_input = torch.randn(batch_size, in_features, height, width).to(device)
    print(f"测试输入形状: {test_input.shape}")
    
    try:
        # 1. 测试原始超图卷积
        print("\n1. 测试原始超图卷积...")
        hypergraph_original = HypergraphConv(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1
        ).to(device)
        
        with torch.no_grad():
            output_original = hypergraph_original(test_input)
        
        print(f"   ✓ 原始超图卷积成功: {output_original.shape}")
        print(f"   ✓ 输出统计: mean={output_original.mean().item():.4f}, std={output_original.std().item():.4f}")
        
        # 2. 测试HypergraphConvDiagonal (简化模式)
        print("\n2. 测试HypergraphConvDiagonal (简化模式)...")
        hypergraph_diagonal_simple = HypergraphConvDiagonal(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1,
            use_diagonal=True,
            simplified_mode=True  # 简化模式
        ).to(device)
        
        with torch.no_grad():
            output_diagonal_simple = hypergraph_diagonal_simple(test_input)
        
        print(f"   ✓ 对角简化版本成功: {output_diagonal_simple.shape}")
        print(f"   ✓ 输出统计: mean={output_diagonal_simple.mean().item():.4f}, std={output_diagonal_simple.std().item():.4f}")
        
        # 3. 测试HypergraphConvDiagonal (完整模式 - 简单融合)
        print("\n3. 测试HypergraphConvDiagonal (完整模式 - 简单融合)...")
        hypergraph_diagonal_full = HypergraphConvDiagonal(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1,
            use_diagonal=True,
            simplified_mode=False,
            max_depth=2,  # 减少深度以提高稳定性
            diagonal_threshold=0.3,
            fusion_strategy='simple_average'
        ).to(device)
        
        with torch.no_grad():
            output_diagonal_full = hypergraph_diagonal_full(test_input)
        
        print(f"   ✓ 对角完整版本(简单融合)成功: {output_diagonal_full.shape}")
        print(f"   ✓ 输出统计: mean={output_diagonal_full.mean().item():.4f}, std={output_diagonal_full.std().item():.4f}")
        
        # 4. 测试HypergraphConvDiagonal (完整模式 - 注意力融合)
        print("\n4. 测试HypergraphConvDiagonal (完整模式 - 注意力融合)...")
        hypergraph_diagonal_attention = HypergraphConvDiagonal(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1,
            use_diagonal=True,
            simplified_mode=False,
            max_depth=2,
            diagonal_threshold=0.3,
            fusion_strategy='weighted_attention'
        ).to(device)
        
        with torch.no_grad():
            output_diagonal_attention = hypergraph_diagonal_attention(test_input)
        
        print(f"   ✓ 对角完整版本(注意力融合)成功: {output_diagonal_attention.shape}")
        print(f"   ✓ 输出统计: mean={output_diagonal_attention.mean().item():.4f}, std={output_diagonal_attention.std().item():.4f}")
        
        # 5. 测试梯度计算
        print("\n5. 测试梯度计算...")
        test_input_grad = test_input.clone().requires_grad_(True)
        
        output = hypergraph_diagonal_full(test_input_grad)
        loss = output.mean()
        loss.backward()
        
        print(f"   ✓ 梯度计算成功")
        print(f"   ✓ 输入梯度范数: {test_input_grad.grad.norm().item():.6f}")
        
        print("\n" + "="*60)
        print("HypergraphConvDiagonal基本功能测试完成！")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """比较不同超图卷积版本的性能"""
    print("\n" + "=" * 60)
    print("性能对比测试")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 测试参数
    batch_size = 8
    in_features = 256
    height = 24
    width = 12
    edges = 256
    filters = 128
    
    test_input = torch.randn(batch_size, in_features, height, width).to(device)
    
    models = {
        'HypergraphConv (原始)': HypergraphConv(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1
        ).to(device),
        
        'HypergraphConvBeta (Beta分布)': HypergraphConvBeta(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1,
            use_beta_distribution=True,
            dataset_name='SYSU-MM01'
        ).to(device),
        
        'HypergraphConvDiagonal (对角索引)': HypergraphConvDiagonal(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1,
            use_diagonal=True,
            simplified_mode=False,
            max_depth=2,
            fusion_strategy='simple_average'
        ).to(device),
        
        'HypergraphConvDiag (完整版)': HypergraphConvDiag(
            in_features=in_features,
            out_features=in_features,
            features_height=height,
            features_width=width,
            edges=edges,
            filters=filters,
            theta1=0.1,
            use_diagonal=True,
            simplified_mode=False,
            use_beta_distribution=True,
            dataset_name='SYSU-MM01'
        ).to(device)
    }
    
    # 性能测试
    import time
    
    for name, model in models.items():
        print(f"\n测试 {name}:")
        
        # 前向传播时间测试
        model.eval()
        times = []
        
        for _ in range(5):  # 预热
            if 'Beta' in name or 'Diag' in name:
                _ = model(test_input, 'visible_original')
            else:
                _ = model(test_input)
        
        torch.cuda.synchronize() if device.type == 'cuda' else None
        
        for _ in range(20):
            start_time = time.time()
            if 'Beta' in name or 'Diag' in name:
                output = model(test_input, 'visible_original')
            else:
                output = model(test_input)
            torch.cuda.synchronize() if device.type == 'cuda' else None
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = sum(times) / len(times)
        print(f"   平均前向传播时间: {avg_time*1000:.2f} ms")
        print(f"   输出形状: {output.shape}")
        print(f"   输出统计: mean={output.mean().item():.4f}, std={output.std().item():.4f}")
        
        # 参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   参数数量: {total_params:,}")

if __name__ == "__main__":
    print("开始测试HypergraphConvDiagonal类...")
    
    # 基本功能测试
    success = test_hypergraph_diagonal_basic()
    
    # 性能对比测试
    if success:
        test_performance_comparison()
        print("\n🎉 HypergraphConvDiagonal类测试完成！")
    else:
        print("\n❌ 测试失败，请检查代码。")
