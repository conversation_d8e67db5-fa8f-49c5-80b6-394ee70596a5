#!/usr/bin/env python3
"""
运行特征可视化实验的简单脚本
"""

import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
from torch.autograd import Variable
import numpy as np
import os
from PIL import Image

from model import embed_net
from config_loader import Config<PERSON>oader
from add_feature_hooks import clear_captured_features, visualize_captured_features

def run_feature_visualization_test(use_pretrained=True):
    """
    运行特征可视化测试
    Args:
        use_pretrained: 是否使用预训练模型
    """
    print("=== 特征可视化实验 ===")

    # 清空之前的特征
    clear_captured_features()

    # 加载配置
    config_loader = ConfigLoader('config.yaml')
    args = config_loader.get_training_args()

    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 创建模型
    net = embed_net(
        class_num=395,  # SYSU数据集
        enable_cross_modal=True,
        num_strips=4,
        projection_dim=128,
        num_heads=4,
    )

    # 启用特征捕获
    net._capture_features = True

    # 可选加载检查点
    model_loaded = False
    if use_pretrained:
        # checkpoint_path = 'result/saved_model/sysu_hsfl_p4_n4_lr_0.1_seed_0_best_gpu1.t'
        checkpoint_path = '../HSFLNet/result/saved_model/sysu_hsfl_p4_n4_lr_0.1_seed_0_best_gpu0.t'
        if os.path.exists(checkpoint_path):
            print(f"加载预训练模型: {checkpoint_path}")
            try:
                checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)

                # 智能加载权重
                model_dict = net.state_dict()
                checkpoint_dict = checkpoint['net']
                filtered_dict = {k: v for k, v in checkpoint_dict.items()
                                if k in model_dict and model_dict[k].shape == v.shape}
                model_dict.update(filtered_dict)
                net.load_state_dict(model_dict)
                print(f"✅ 成功加载 {len(filtered_dict)}/{len(checkpoint_dict)} 个参数")
                model_loaded = True
            except Exception as e:
                print(f"❌ 加载预训练模型失败: {e}")
                print("将使用随机初始化的模型")
        else:
            print(f"❌ 预训练模型文件不存在: {checkpoint_path}")
            print("将使用随机初始化的模型")

    if not model_loaded:
        print("🎲 使用随机初始化的模型进行特征可视化")
        print("   注意: 随机特征可能无法反映真实的语义信息")
    
    net.to(device)
    net.eval()  # 设置为评估模式
    
    # 准备测试数据
    print("准备测试数据...")
    
    # 数据预处理
    normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    transform_test = transforms.Compose([
        transforms.ToPILImage(),
        transforms.Resize((384, 192)),
        transforms.ToTensor(),
        normalize,
    ])
    
    # 尝试加载真实数据
    try:
        from data_manager import process_query_sysu
        data_path = '../DataSets/SYSU-MM01/'
        
        if os.path.exists(data_path):
            print("加载SYSU-MM01数据集...")
            query_img, query_label, query_cam = process_query_sysu(data_path, mode='all')
            
            # 只取前50个样本进行可视化
            num_samples = min(50, len(query_img))
            test_data = []
            test_labels = []
            
            for i in range(num_samples):
                # query_img[i] 是图像路径，需要先加载图像
                img_path = query_img[i]
                img = Image.open(img_path).convert('RGB')  # 确保是RGB格式
                img_tensor = transform_test(img)
                test_data.append(img_tensor)
                test_labels.append(query_label[i])
            
            print(f"成功加载 {num_samples} 个真实样本")
            
        else:
            raise FileNotFoundError("数据集路径不存在")
            
    except Exception as e:
        print(f"加载真实数据失败: {e}")
        print("使用随机数据进行演示...")
        
        # 生成随机数据
        num_samples = 30
        test_data = []
        test_labels = []
        
        for i in range(num_samples):
            # 生成随机图像
            img_tensor = torch.randn(3, 384, 192)
            test_data.append(img_tensor)
            test_labels.append(i % 10)  # 10个不同的类别
    
    # 运行前向传播以捕获特征
    print("开始特征提取...")
    
    with torch.no_grad():
        for i, (img_tensor, label) in enumerate(zip(test_data, test_labels)):
            if i % 10 == 0:
                print(f"处理样本 {i+1}/{len(test_data)}")
            
            # 准备输入
            input_tensor = img_tensor.unsqueeze(0).to(device)  # [1, 3, H, W]
            
            # 模拟四流输入（可见光原图、增强图、红外原图、增强图）
            x1_1 = input_tensor  # 可见光原图
            x1_2 = input_tensor  # 可见光增强图
            x2_1 = input_tensor  # 红外原图  
            x2_2 = input_tensor  # 红外增强图
            
            # 创建身份标签
            identities = torch.tensor([label]).to(device)
            
            # 前向传播
            try:
                output = net(x1_1, x1_2, x2_1, x2_2, modal=0, identities=identities)
            except Exception as e:
                print(f"前向传播出错: {e}")
                continue
    
    print("特征提取完成！")
    
    # 可视化特征
    print("开始生成可视化...")
    stats = visualize_captured_features(save_dir='feature_vis_results')
    
    if stats:
        print("\n=== 特征分析结果 ===")
        print(f"BN特征范数均值: {stats['bn_norm_mean']:.4f}")
        print(f"超图特征范数均值: {stats['graph_norm_mean']:.4f}")
        print(f"特征相似度均值: {stats['similarity_mean']:.4f}")
        print(f"特征相似度标准差: {stats['similarity_std']:.4f}")
        
        # 分析结果
        if stats['similarity_mean'] > 0.9:
            print("\n✅ 分析结论: BN后特征和超图卷积后特征高度相似")
            print("   这解释了为什么跨模态对比学习在两个位置的效果相同")
        elif stats['similarity_mean'] > 0.7:
            print("\n📊 分析结论: BN后特征和超图卷积后特征较为相似")
            print("   超图卷积对特征有一定改进，但保持了主要的语义信息")
        else:
            print("\n🔍 分析结论: BN后特征和超图卷积后特征差异较大")
            print("   超图卷积显著改变了特征表示")
        
        # 范数比较
        norm_ratio = stats['graph_norm_mean'] / stats['bn_norm_mean']
        if norm_ratio > 1.2:
            print(f"   超图卷积增强了特征强度 (范数比例: {norm_ratio:.2f})")
        elif norm_ratio < 0.8:
            print(f"   超图卷积降低了特征强度 (范数比例: {norm_ratio:.2f})")
        else:
            print(f"   超图卷积保持了特征强度 (范数比例: {norm_ratio:.2f})")
    
    print(f"\n实验完成！可视化结果已保存到 feature_vis_results/ 目录")
    print(f"模型状态: {'预训练模型' if model_loaded else '随机初始化'}")
    return stats

def quick_test(use_pretrained=False):
    """
    快速测试（使用更少的样本）
    Args:
        use_pretrained: 是否使用预训练模型
    """
    print("=== 快速特征可视化测试 ===")

    # 清空之前的特征
    clear_captured_features()

    # 创建简单模型
    net = embed_net(class_num=10, enable_cross_modal=False)  # 禁用跨模态以简化
    net._capture_features = True

    # 可选加载预训练模型
    if use_pretrained:
        # checkpoint_path = 'result/saved_model/sysu_hsfl_p4_n4_lr_0.1_seed_0_best_gpu1.t'
        checkpoint_path = '../HSFLNet/result/saved_model/sysu_hsfl_p4_n4_lr_0.1_seed_0_best_gpu0.t'
        if os.path.exists(checkpoint_path):
            try:
                print("加载预训练模型...")
                checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
                model_dict = net.state_dict()
                checkpoint_dict = checkpoint['net']
                filtered_dict = {k: v for k, v in checkpoint_dict.items()
                                if k in model_dict and model_dict[k].shape == v.shape}
                model_dict.update(filtered_dict)
                net.load_state_dict(model_dict)
                print(f"✅ 加载了 {len(filtered_dict)} 个参数")
            except Exception as e:
                print(f"❌ 加载失败: {e}")

    net.eval()

    # 生成随机数据
    print("生成随机测试数据...")
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    net.to(device)

    with torch.no_grad():
        for i in range(10):  # 只用10个样本
            # 随机输入
            x = torch.randn(1, 3, 384, 192).to(device)
            identities = torch.tensor([i % 5]).to(device)  # 5个类别

            try:
                # 前向传播
                output = net(x, x, x, x, modal=0, identities=identities)
            except Exception as e:
                print(f"样本 {i} 处理失败: {e}")
                continue

    # 可视化
    print("生成可视化...")
    stats = visualize_captured_features(save_dir='quick_test_results')

    print("快速测试完成！")
    return stats

if __name__ == "__main__":
    import sys

    # 解析命令行参数
    use_pretrained = True  # 默认使用预训练模型
    quick_mode = False

    for arg in sys.argv[1:]:
        if arg == 'quick':
            quick_mode = True
        elif arg == 'no-pretrained':
            use_pretrained = False
        elif arg == 'random':
            use_pretrained = False

    print(f"运行模式: {'快速测试' if quick_mode else '完整实验'}")
    print(f"模型类型: {'预训练模型' if use_pretrained else '随机初始化'}")
    print()

    if quick_mode:
        quick_test(use_pretrained)
    else:
        run_feature_visualization_test(use_pretrained)
