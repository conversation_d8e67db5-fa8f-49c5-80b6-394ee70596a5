"""
测试跨模态对比学习模块
验证改进后的模块是否能正常工作
"""

import torch
import torch.nn as nn
import numpy as np
from simple_cross_modal import SimpleCrossModalContrastive, create_simple_strips_batch, CrossModalLossMonitor
from progressive_cross_modal import ProgressiveCrossModalTrainer, diagnose_cross_modal_issues

def test_simple_cross_modal():
    """测试简化的跨模态对比学习模块"""
    print("="*50)
    print("测试简化的跨模态对比学习模块")
    print("="*50)
    
    # 创建模块
    module = SimpleCrossModalContrastive(
        feat_dim=256,
        num_strips=3,
        temperature=0.1,
        projection_dim=64
    )
    
    # 创建测试数据
    batch_size = 8
    num_modalities = 4
    num_strips = 3
    feat_dim = 256
    
    # 模拟特征 [B, 4, num_strips, feat_dim]
    modal_strips = torch.randn(batch_size, num_modalities, num_strips, feat_dim)
    identities = torch.randint(0, 10, (batch_size,))
    
    print(f"输入形状: {modal_strips.shape}")
    print(f"身份标签: {identities}")
    
    # 前向传播
    try:
        projected_features, contrastive_loss = module(modal_strips, identities)
        
        print(f"投影特征形状: {projected_features.shape}")
        print(f"对比损失: {contrastive_loss.item():.4f}")
        
        # 检查输出有效性
        assert not torch.isnan(contrastive_loss), "对比损失包含NaN"
        assert not torch.isinf(contrastive_loss), "对比损失包含Inf"
        assert contrastive_loss.item() >= 0, "对比损失为负数"
        
        print("✓ 简化模块测试通过")
        
    except Exception as e:
        print(f"✗ 简化模块测试失败: {e}")
        return False
    
    return True

def test_strips_creation():
    """测试条带创建函数"""
    print("\n" + "="*50)
    print("测试条带创建函数")
    print("="*50)
    
    # 创建测试特征图
    batch_size = 4
    num_modalities = 4
    channels = 256
    height, width = 24, 12
    
    feat_maps = torch.randn(batch_size, num_modalities, channels, height, width)
    print(f"输入特征图形状: {feat_maps.shape}")
    
    # 创建条带
    try:
        strips = create_simple_strips_batch(feat_maps, num_strips=3)
        print(f"输出条带形状: {strips.shape}")
        
        expected_shape = (batch_size, num_modalities, 3, channels)
        assert strips.shape == expected_shape, f"形状不匹配: 期望{expected_shape}, 实际{strips.shape}"
        
        print("✓ 条带创建测试通过")
        
    except Exception as e:
        print(f"✗ 条带创建测试失败: {e}")
        return False
    
    return True

def test_progressive_trainer():
    """测试渐进式训练器"""
    print("\n" + "="*50)
    print("测试渐进式训练器")
    print("="*50)
    
    # 创建模拟模型
    class MockModel:
        def __init__(self):
            self.contrastive_weight = 0.5
    
    model = MockModel()
    
    # 创建训练器
    trainer = ProgressiveCrossModalTrainer(
        model=model,
        initial_weight=0.01,
        max_weight=0.3,
        warmup_epochs=10,
        stable_epochs=20
    )
    
    print("测试权重调度:")
    test_epochs = [0, 5, 10, 15, 30, 50]
    
    for epoch in test_epochs:
        weight = trainer.get_current_weight(epoch)
        should_enable = trainer.should_enable_contrastive(epoch)
        print(f"Epoch {epoch:2d}: 权重={weight:.4f}, 启用={should_enable}")
    
    # 测试权重更新
    new_weight = trainer.update_weight(5)
    assert abs(model.contrastive_weight - new_weight) < 1e-6, "权重更新失败"
    
    print("✓ 渐进式训练器测试通过")
    return True

def test_loss_monitor():
    """测试损失监控器"""
    print("\n" + "="*50)
    print("测试损失监控器")
    print("="*50)
    
    monitor = CrossModalLossMonitor(window_size=10)
    
    # 模拟损失序列
    loss_sequence = [2.5, 2.3, 2.1, 2.0, 1.9, 1.8, 1.7, 1.6, 1.5, 1.4]
    
    for i, loss in enumerate(loss_sequence):
        monitor.update(loss, grad_norm=0.5)
        
        if i >= 5:  # 有足够数据后开始统计
            stats = monitor.get_statistics()
            print(f"Step {i}: 平均损失={stats['mean_loss']:.3f}, 趋势={stats['recent_trend']}")
    
    # 测试调整建议
    should_adjust, reason = monitor.should_adjust_weight()
    print(f"是否需要调整权重: {should_adjust}, 原因: {reason}")
    
    print("✓ 损失监控器测试通过")
    return True

def test_numerical_stability():
    """测试数值稳定性"""
    print("\n" + "="*50)
    print("测试数值稳定性")
    print("="*50)
    
    module = SimpleCrossModalContrastive(
        feat_dim=256,
        num_strips=3,
        temperature=0.1,
        projection_dim=64
    )
    
    # 测试极端情况
    test_cases = [
        ("正常情况", torch.randn(4, 4, 3, 256)),
        ("小值", torch.randn(4, 4, 3, 256) * 0.001),
        ("大值", torch.randn(4, 4, 3, 256) * 100),
        ("零值", torch.zeros(4, 4, 3, 256)),
    ]
    
    identities = torch.randint(0, 5, (4,))
    
    for case_name, modal_strips in test_cases:
        try:
            _, loss = module(modal_strips, identities)
            
            is_valid = not (torch.isnan(loss) or torch.isinf(loss))
            status = "✓" if is_valid else "✗"
            print(f"{status} {case_name}: 损失={loss.item():.4f}, 有效={is_valid}")
            
        except Exception as e:
            print(f"✗ {case_name}: 异常 - {e}")
    
    print("✓ 数值稳定性测试完成")
    return True

def test_gradient_flow():
    """测试梯度流"""
    print("\n" + "="*50)
    print("测试梯度流")
    print("="*50)
    
    module = SimpleCrossModalContrastive(
        feat_dim=256,
        num_strips=3,
        temperature=0.1,
        projection_dim=64
    )
    
    # 创建需要梯度的输入
    modal_strips = torch.randn(4, 4, 3, 256, requires_grad=True)
    identities = torch.randint(0, 5, (4,))
    
    # 前向传播
    _, loss = module(modal_strips, identities)
    
    # 反向传播
    loss.backward()
    
    # 检查梯度
    has_grad = modal_strips.grad is not None
    if has_grad:
        grad_norm = modal_strips.grad.norm().item()
        grad_finite = torch.isfinite(modal_strips.grad).all().item()
        print(f"梯度范数: {grad_norm:.4f}")
        print(f"梯度有限: {grad_finite}")
        
        # 检查模块参数梯度
        param_grads = []
        for name, param in module.named_parameters():
            if param.grad is not None:
                param_grads.append((name, param.grad.norm().item()))
        
        print("参数梯度:")
        for name, grad_norm in param_grads:
            print(f"  {name}: {grad_norm:.4f}")
    
    status = "✓" if has_grad and grad_finite else "✗"
    print(f"{status} 梯度流测试完成")
    
    return has_grad and grad_finite

def run_all_tests():
    """运行所有测试"""
    print("开始跨模态对比学习模块测试")
    print("="*70)
    
    tests = [
        ("简化模块", test_simple_cross_modal),
        ("条带创建", test_strips_creation),
        ("渐进式训练器", test_progressive_trainer),
        ("损失监控器", test_loss_monitor),
        ("数值稳定性", test_numerical_stability),
        ("梯度流", test_gradient_flow),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*70)
    print("测试总结")
    print("="*70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:15s}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！跨模态对比学习模块可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
    
    return passed == total

if __name__ == "__main__":
    # 设置随机种子确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\n建议的下一步:")
        print("1. 修改 config.yaml 启用跨模态对比学习")
        print("2. 运行 python train.py --config config.yaml")
        print("3. 观察训练日志中的对比损失变化")
        print("4. 根据需要调整参数")
    else:
        print("\n请先解决测试中发现的问题，然后再进行训练。")
