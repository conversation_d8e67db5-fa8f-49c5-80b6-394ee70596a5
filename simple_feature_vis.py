#!/usr/bin/env python3
"""
简化版特征可视化实验
直接修改模型forward函数来捕获BN后和超图卷积后的特征
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import os
from model import embed_net
from config_loader import ConfigLoader
import torchvision.transforms as transforms

# 全局变量存储特征
features_bn_global = []
features_graph_global = []
labels_global = []

def modified_forward_hook(model, input_data, labels, max_samples=200):
    """
    修改后的前向传播，捕获BN后和超图卷积后的特征
    """
    global features_bn_global, features_graph_global, labels_global
    
    model.eval()
    sample_count = 0
    
    with torch.no_grad():
        for batch_idx, (input_batch, label_batch) in enumerate(zip(input_data, labels)):
            if sample_count >= max_samples:
                break
                
            # 确保输入是正确的格式
            if len(input_batch.shape) == 3:  # [C, H, W]
                input_batch = input_batch.unsqueeze(0)  # [1, C, H, W]
            
            input_batch = input_batch.cuda()
            
            # 模拟四流输入
            x1_1, x1_2, x2_1, x2_2 = input_batch, input_batch, input_batch, input_batch
            modal = 0
            
            # 前向传播到特征提取
            x1_1 = model.visible_module(x1_1)
            x2_1 = model.thermal_module(x2_1)
            x1_2 = model.visible_moduleA(x1_2)
            x2_2 = model.thermal_moduleA(x2_2)
            x = torch.cat((x1_1, x1_2, x2_1, x2_2), 0)
            
            # 共享层
            x_low = x
            x = model.base_resnet.base.layer1(x)
            x = model.SSM1(x, x_low)
            x_low = x
            x = model.base_resnet.base.layer2(x)
            x = model.SSM2(x, x_low)
            x = model.base_resnet.base.layer3(x)
            x = model.base_resnet.base.layer4(x)
            global_feat = x
            
            # 生成掩码
            masks = model.spatial_attention(global_feat)
            masks = model.activation(masks)
            
            # 对每个part提取特征
            for i in range(model.part_num):
                mask = masks[:, i:i+1, :, :]
                x = mask * global_feat
                x = model.conv_reduce(x)
                
                # BN后特征
                x_bn = model.bn_conv_reduce(x)
                
                # 超图卷积后特征
                if hasattr(model.hypergraph, 'use_beta_distribution') and model.hypergraph.use_beta_distribution:
                    feat_graph = model.graphw * model.hypergraph(x_bn, 'visible_original')
                else:
                    feat_graph = model.graphw * model.hypergraph(x_bn)
                
                # 池化并展平
                feat_bn_pooled = F.avg_pool2d(x_bn, x_bn.size()[2:])
                feat_bn_pooled = feat_bn_pooled.view(feat_bn_pooled.size(0), -1)
                
                feat_graph_pooled = F.avg_pool2d(feat_graph, feat_graph.size()[2:])
                feat_graph_pooled = feat_graph_pooled.view(feat_graph_pooled.size(0), -1)
                
                # 存储特征（只取第一个样本，因为是4个模态的拼接）
                features_bn_global.append(feat_bn_pooled[0:1].cpu().numpy())
                features_graph_global.append(feat_graph_pooled[0:1].cpu().numpy())
                labels_global.append(label_batch.item())
            
            sample_count += 1
            if sample_count % 20 == 0:
                print(f"已处理 {sample_count} 个样本")

def load_test_data(data_path, num_samples=200):
    """加载测试数据"""
    try:
        from data_manager import process_query_sysu
        query_img, query_label, query_cam = process_query_sysu(data_path, mode='all')
        
        # 数据预处理
        normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        transform_test = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((384, 192)),
            transforms.ToTensor(),
            normalize,
        ])
        
        # 转换数据
        processed_imgs = []
        processed_labels = []
        
        for i in range(min(num_samples, len(query_img))):
            img_tensor = transform_test(query_img[i])
            processed_imgs.append(img_tensor)
            processed_labels.append(torch.tensor(query_label[i]))
        
        return processed_imgs, processed_labels
        
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None, None

def visualize_features(save_dir='feature_vis'):
    """可视化特征对比"""
    global features_bn_global, features_graph_global, labels_global
    
    if not features_bn_global:
        print("没有特征数据可视化")
        return
    
    os.makedirs(save_dir, exist_ok=True)
    
    # 转换为numpy数组
    features_bn = np.vstack(features_bn_global)
    features_graph = np.vstack(features_graph_global)
    labels = np.array(labels_global)
    
    print(f"特征形状: BN {features_bn.shape}, 超图 {features_graph.shape}")
    
    # 1. 计算统计信息
    print("\n=== 特征统计信息 ===")
    
    # 特征范数
    bn_norms = np.linalg.norm(features_bn, axis=1)
    graph_norms = np.linalg.norm(features_graph, axis=1)
    print(f"BN特征范数: 均值={bn_norms.mean():.4f}, 标准差={bn_norms.std():.4f}")
    print(f"超图特征范数: 均值={graph_norms.mean():.4f}, 标准差={graph_norms.std():.4f}")
    
    # 余弦相似度
    similarities = []
    for i in range(len(features_bn)):
        sim = np.dot(features_bn[i], features_graph[i]) / (
            np.linalg.norm(features_bn[i]) * np.linalg.norm(features_graph[i]) + 1e-8
        )
        similarities.append(sim)
    
    similarities = np.array(similarities)
    print(f"余弦相似度: 均值={similarities.mean():.4f}, 标准差={similarities.std():.4f}")
    
    # 2. t-SNE可视化
    print("\n生成t-SNE可视化...")
    
    # 随机采样用于可视化
    n_vis = min(500, len(features_bn))
    indices = np.random.choice(len(features_bn), n_vis, replace=False)
    
    feat_bn_vis = features_bn[indices]
    feat_graph_vis = features_graph[indices]
    labels_vis = labels[indices]
    
    # t-SNE降维
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    feat_bn_2d = tsne.fit_transform(feat_bn_vis)
    feat_graph_2d = tsne.fit_transform(feat_graph_vis)
    
    # 绘制对比图
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # 选择前10个类别
    unique_labels = np.unique(labels_vis)[:10]
    colors = plt.cm.tab10(np.linspace(0, 1, len(unique_labels)))
    
    # BN特征
    for i, label in enumerate(unique_labels):
        mask = labels_vis == label
        if np.any(mask):
            axes[0].scatter(feat_bn_2d[mask, 0], feat_bn_2d[mask, 1], 
                           c=[colors[i]], label=f'ID {label}', alpha=0.7, s=30)
    axes[0].set_title('BN后特征 (t-SNE)')
    axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 超图特征
    for i, label in enumerate(unique_labels):
        mask = labels_vis == label
        if np.any(mask):
            axes[1].scatter(feat_graph_2d[mask, 0], feat_graph_2d[mask, 1], 
                           c=[colors[i]], label=f'ID {label}', alpha=0.7, s=30)
    axes[1].set_title('超图卷积后特征 (t-SNE)')
    axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_comparison_tsne.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. 特征分布对比
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 范数分布
    axes[0, 0].hist(bn_norms, bins=30, alpha=0.7, label='BN特征', color='blue')
    axes[0, 0].hist(graph_norms, bins=30, alpha=0.7, label='超图特征', color='red')
    axes[0, 0].set_title('特征范数分布')
    axes[0, 0].set_xlabel('L2范数')
    axes[0, 0].legend()
    
    # 相似度分布
    axes[0, 1].hist(similarities, bins=30, alpha=0.7, color='green')
    axes[0, 1].set_title('余弦相似度分布')
    axes[0, 1].set_xlabel('余弦相似度')
    
    # 特征均值对比
    bn_mean = features_bn.mean(axis=0)
    graph_mean = features_graph.mean(axis=0)
    axes[1, 0].plot(bn_mean[:50], label='BN特征', color='blue')
    axes[1, 0].plot(graph_mean[:50], label='超图特征', color='red')
    axes[1, 0].set_title('特征均值对比 (前50维)')
    axes[1, 0].legend()
    
    # 特征差异
    diff = np.abs(features_bn - features_graph).mean(axis=0)
    axes[1, 1].plot(diff[:50], color='purple')
    axes[1, 1].set_title('特征差异 (前50维)')
    axes[1, 1].set_xlabel('特征维度')
    axes[1, 1].set_ylabel('平均绝对差异')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_statistics.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"可视化结果已保存到 {save_dir}/")
    
    return {
        'bn_norm_mean': bn_norms.mean(),
        'graph_norm_mean': graph_norms.mean(),
        'similarity_mean': similarities.mean(),
        'similarity_std': similarities.std()
    }

def run_simple_experiment():
    """运行简化版特征可视化实验"""
    global features_bn_global, features_graph_global, labels_global
    
    print("=== 简化版特征可视化实验 ===")
    
    # 清空全局变量
    features_bn_global = []
    features_graph_global = []
    labels_global = []
    
    # 加载配置
    config_loader = ConfigLoader('config.yaml')
    args = config_loader.get_training_args()
    
    # 创建模型
    net = embed_net(
        class_num=395,
        enable_cross_modal=True,
        num_strips=4,
        projection_dim=128,
        num_heads=4,
    )
    
    # 加载检查点
    checkpoint_path = 'result/saved_model/sysu_hsfl_p4_n4_lr_0.1_seed_0_best_gpu1.t'
    if os.path.exists(checkpoint_path):
        print(f"加载检查点: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location='cuda', weights_only=False)
        
        model_dict = net.state_dict()
        checkpoint_dict = checkpoint['net']
        filtered_dict = {k: v for k, v in checkpoint_dict.items() 
                        if k in model_dict and model_dict[k].shape == v.shape}
        model_dict.update(filtered_dict)
        net.load_state_dict(model_dict)
        print(f"成功加载 {len(filtered_dict)}/{len(checkpoint_dict)} 个参数")
    
    net.cuda()
    
    # 加载测试数据
    data_path = '../DataSets/SYSU-MM01/'
    print("加载测试数据...")
    test_imgs, test_labels = load_test_data(data_path, num_samples=100)
    
    if test_imgs is None:
        print("数据加载失败，使用随机数据进行演示")
        # 生成随机数据用于演示
        test_imgs = [torch.randn(3, 384, 192) for _ in range(50)]
        test_labels = [torch.randint(0, 50, (1,)).item() for _ in range(50)]
    
    # 提取特征
    print("开始提取特征...")
    modified_forward_hook(net, test_imgs, test_labels, max_samples=50)
    
    # 可视化
    print("生成可视化...")
    stats = visualize_features()
    
    print("实验完成！")
    return stats

if __name__ == "__main__":
    run_simple_experiment()
